
import logging
import os
import pdf2zh_next
from pdf2zh_next.high_level import do_translate_file_async
from pdf2zh_next.config.model import SettingsModel

async def translate_pdf(input_file: str, output_file: str, translation_setting: SettingsModel):
    """将PDF文件翻译为目标语言"""
    setting = SettingsModel(
        translation=pdf2zh_next.TranslationSettings(
            qps=10
        ),
        translate_engine_settings= pdf2zh_next.OpenAISettings(
            openai_api_key="sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
            openai_model="gpt-3.5-turbo",
            openai_base_url= ""
        )

    )
    await do_translate_file_async()
    pass


