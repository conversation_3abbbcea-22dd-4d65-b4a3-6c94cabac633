#!/usr/bin/env python3
"""
离线系统验证脚本
快速验证系统在完全离线环境下的工作状态
"""

import sys
import logging
from datetime import datetime

# 设置简洁的日志格式
logging.basicConfig(level=logging.WARNING)

def main():
    """主验证函数"""
    print("🔒 离线许可证系统验证")
    print("=" * 40)
    
    try:
        # 1. 验证配置安全性
        print("1. 检查配置安全性...")
        from core.license_config import license_config
        
        min_score = license_config.get_min_security_score(strict_mode=True)
        max_jump = license_config.get_max_time_jump()
        
        if min_score >= 75 and max_jump <= 300:
            print(f"   ✅ 安全参数正确 (评分≥{min_score}, 跳跃≤{max_jump}s)")
        else:
            print(f"   ❌ 安全参数异常 (评分={min_score}, 跳跃={max_jump}s)")
            return False
        
        # 2. 验证离线时间验证
        print("2. 检查离线时间验证...")
        from core.offline_time_validator import offline_time_validator
        
        offline_time_validator.set_offline_only_mode(True)
        
        if offline_time_validator.is_offline_only_mode():
            print("   ✅ 完全离线模式已启用")
        else:
            print("   ❌ 离线模式未正确启用")
            return False
        
        # 3. 验证许可证管理器
        print("3. 检查许可证管理器...")
        from license.license_manager import LicenseManager
        
        manager = LicenseManager(offline_mode=True, strict_mode=True)
        
        if manager.offline_mode and manager.strict_mode and manager.min_security_score >= 75:
            print(f"   ✅ 管理器配置正确 (严格模式, 评分≥{manager.min_security_score})")
        else:
            print(f"   ❌ 管理器配置异常")
            return False
        
        # 4. 验证加密系统
        print("4. 检查加密系统...")
        from license.crypto_utils import crypto_manager
        
        test_data = {"test": "data", "timestamp": datetime.now().isoformat()}
        encrypted = crypto_manager.encrypt_data(test_data)
        decrypted = crypto_manager.decrypt_data(encrypted)
        
        if decrypted == test_data:
            print("   ✅ 加密系统工作正常")
        else:
            print("   ❌ 加密系统异常")
            return False
        
        # 5. 验证CLI集成
        print("5. 检查CLI集成...")
        from core.auth import PermissionManager
        
        if PermissionManager.is_offline_mode():
            print("   ✅ CLI离线模式集成正常")
        else:
            print("   ⚠️  CLI未检测到离线模式")
        
        print("\n" + "=" * 40)
        print("🎉 系统验证完成！")
        print("✅ 所有核心组件工作正常")
        print("🔒 系统可以安全地在完全离线环境中运行")
        print("\n💡 提示：")
        print("   - 安全参数已硬编码，用户无法篡改")
        print("   - 系统不会尝试任何网络连接")
        print("   - 时间验证使用多个本地时间源")
        print("   - 严格模式下要求85分以上安全评分")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保所有依赖已正确安装")
        return False
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
