"""
增强的许可证管理器
集成安全时间验证，防止时间篡改攻击
"""

import os
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import logging

from license.crypto_utils import crypto_manager
from .secure_time_validator import secure_time_validator, TimeValidationResult

logger = logging.getLogger(__name__)

class LicenseStatus(Enum):
    VALID = "valid"
    EXPIRED = "expired"
    INVALID = "invalid"
    NOT_FOUND = "not_found"
    CORRUPTED = "corrupted"
    TIME_TAMPERED = "time_tampered"  # 新增：时间篡改状态

@dataclass
class EnhancedLicenseInfo:
    """增强的许可证信息结构"""
    organization: str
    user_id: str
    modules: List[str]
    issue_date: str
    start_date: str
    end_date: str
    max_executions: int
    current_executions: int
    license_id: str
    signature: str
    # 新增安全字段
    time_proof: str = ""           # 时间证明
    last_validation: str = ""      # 最后验证时间
    security_level: str = "standard"  # 安全级别

class EnhancedLicenseManager:
    """增强的许可证管理器"""
    
    def __init__(self, license_file: str = None):
        self.license_file = license_file or self._get_default_license_path()
        self._license_info: Optional[EnhancedLicenseInfo] = None
        self._license_status: LicenseStatus = LicenseStatus.NOT_FOUND
        self._time_validation_result: Optional[TimeValidationResult] = None
        
    def _get_default_license_path(self) -> str:
        """获取默认许可证文件路径"""
        config_dir = Path(__file__).parent.parent / "config"
        return str(config_dir / "app.license")
    
    def load_license(self, strict_time_validation: bool = True) -> Tuple[LicenseStatus, Optional[EnhancedLicenseInfo]]:
        """
        加载并验证许可证
        
        Args:
            strict_time_validation: 是否启用严格时间验证
        """
        if not os.path.exists(self.license_file):
            self._license_status = LicenseStatus.NOT_FOUND
            return self._license_status, None
        
        try:
            # 读取加密的许可证文件
            with open(self.license_file, 'r', encoding='utf-8') as f:
                encrypted_data = f.read().strip()
            
            # 解密许可证数据
            license_data = crypto_manager.decrypt_data(encrypted_data)
            
            # 验证签名
            signature = license_data.pop('signature', '')
            if not crypto_manager.verify_signature(license_data, signature):
                self._license_status = LicenseStatus.CORRUPTED
                return self._license_status, None
            
            # 重新添加签名到数据中
            license_data['signature'] = signature
            
            # 兼容旧版许可证格式
            if 'time_proof' not in license_data:
                license_data['time_proof'] = ""
            if 'last_validation' not in license_data:
                license_data['last_validation'] = ""
            if 'security_level' not in license_data:
                license_data['security_level'] = "standard"
            
            # 创建许可证信息对象
            self._license_info = EnhancedLicenseInfo(**license_data)
            
            # 执行增强的许可证验证
            self._license_status = self._validate_license_enhanced(
                self._license_info, strict_time_validation
            )
            
            return self._license_status, self._license_info
            
        except Exception as e:
            logger.error(f"许可证加载失败: {e}")
            self._license_status = LicenseStatus.CORRUPTED
            return self._license_status, None
    
    def _validate_license_enhanced(self, license_info: EnhancedLicenseInfo, 
                                 strict_validation: bool = True) -> LicenseStatus:
        """增强的许可证验证"""
        
        # 1. 执行安全时间验证
        time_result = secure_time_validator.validate_license_time(
            license_info.start_date, 
            license_info.end_date
        )
        self._time_validation_result = time_result
        
        # 2. 检查时间验证结果
        if not time_result.is_valid:
            logger.warning(f"时间验证失败: {time_result.warnings}")
            return LicenseStatus.EXPIRED
        
        # 3. 严格模式下检查时间可信度
        if strict_validation and time_result.confidence_level == "low":
            logger.warning("时间验证可信度低，可能存在时间篡改")
            if any("篡改" in warning for warning in time_result.warnings):
                return LicenseStatus.TIME_TAMPERED
        
        # 4. 验证时间证明（防回滚攻击）
        if license_info.time_proof and license_info.last_validation:
            current_proof = secure_time_validator.create_time_proof(asdict(license_info))
            if not secure_time_validator.verify_time_proof(current_proof, license_info.time_proof):
                logger.warning("时间证明验证失败，可能存在回滚攻击")
                return LicenseStatus.TIME_TAMPERED
        
        # 5. 检查执行次数限制
        if (license_info.max_executions > 0 and 
            license_info.current_executions >= license_info.max_executions):
            return LicenseStatus.EXPIRED
        
        # 6. 更新最后验证时间
        license_info.last_validation = time_result.current_time.isoformat()
        
        return LicenseStatus.VALID
    
    def increment_execution_count(self) -> bool:
        """增加执行计数（增强版）"""
        if (self._license_status == LicenseStatus.VALID and 
            self._license_info and 
            (self._license_info.max_executions == -1 or 
             self._license_info.current_executions < self._license_info.max_executions)):
            
            # 更新执行次数
            self._license_info.current_executions += 1
            
            # 生成新的时间证明
            self._license_info.time_proof = secure_time_validator.create_time_proof(
                asdict(self._license_info)
            )
            
            # 保存更新后的许可证
            self._save_updated_license()
            return True
        return False
    
    def _save_updated_license(self):
        """保存更新后的许可证（增强版）"""
        if not self._license_info:
            return
        
        try:
            # 准备数据
            license_data = asdict(self._license_info)
            signature = license_data.pop('signature')
            
            # 重新生成签名
            new_signature = crypto_manager.generate_signature(license_data)
            license_data['signature'] = new_signature
            
            # 加密并保存
            encrypted_data = crypto_manager.encrypt_data(license_data)
            with open(self.license_file, 'w', encoding='utf-8') as f:
                f.write(encrypted_data)
                
            # 更新内存中的签名
            self._license_info.signature = new_signature
            
            logger.info("许可证更新成功")
            
        except Exception as e:
            logger.error(f"保存许可证失败: {e}")
    
    def get_security_status(self) -> Dict[str, any]:
        """获取安全状态报告"""
        if not self._time_validation_result:
            return {"status": "unknown", "message": "未进行时间验证"}
        
        return {
            "time_confidence": self._time_validation_result.confidence_level,
            "current_time": self._time_validation_result.current_time.isoformat(),
            "warnings": self._time_validation_result.warnings,
            "license_status": self._license_status.value,
            "security_recommendations": self._get_security_recommendations()
        }
    
    def _get_security_recommendations(self) -> List[str]:
        """获取安全建议"""
        recommendations = []
        
        if not self._time_validation_result:
            return recommendations
        
        if self._time_validation_result.confidence_level == "low":
            recommendations.append("建议检查网络连接以获取准确时间")
            recommendations.append("确保系统时间设置正确")
        
        if any("篡改" in warning for warning in self._time_validation_result.warnings):
            recommendations.append("检测到可能的时间篡改，建议重新同步系统时间")
            recommendations.append("考虑启用网络时间同步服务")
        
        if self._license_status == LicenseStatus.TIME_TAMPERED:
            recommendations.append("许可证安全验证失败，请联系管理员")
            recommendations.append("不要手动修改系统时间")
        
        return recommendations
    
    def get_status_message(self) -> str:
        """获取状态消息（增强版）"""
        if self._license_status == LicenseStatus.VALID:
            if self._license_info and self._time_validation_result:
                end_date = datetime.fromisoformat(self._license_info.end_date)
                days_left = (end_date - self._time_validation_result.current_time).days
                exec_info = ""
                if self._license_info.max_executions > 0:
                    remaining = self._license_info.max_executions - self._license_info.current_executions
                    exec_info = f", 剩余执行次数: {remaining}"
                
                confidence_info = f"时间验证可信度: {self._time_validation_result.confidence_level}"
                
                return (f"许可证有效 - 授权给: {self._license_info.organization}\n"
                       f"到期时间: {self._license_info.end_date} ({days_left}天后){exec_info}\n"
                       f"授权模块: {', '.join(self._license_info.modules)}\n"
                       f"{confidence_info}")
        
        elif self._license_status == LicenseStatus.TIME_TAMPERED:
            return "检测到时间篡改，许可证验证失败。请确保系统时间正确并联系管理员。"
        
        elif self._license_status == LicenseStatus.EXPIRED:
            return "许可证已过期，请联系管理员更新许可证"
        
        elif self._license_status == LicenseStatus.NOT_FOUND:
            return "未找到许可证文件，请联系管理员获取许可证"
        
        elif self._license_status == LicenseStatus.CORRUPTED:
            return "许可证文件损坏或被篡改，请联系管理员重新获取许可证"
        
        else:
            return "许可证无效，请联系管理员"
    
    # 保持向后兼容的方法
    def get_authorized_modules(self) -> List[str]:
        """获取授权的模块列表"""
        if self._license_status == LicenseStatus.VALID and self._license_info:
            return self._license_info.modules.copy()
        return []
    
    def is_module_authorized(self, module_name: str) -> bool:
        """检查模块是否被授权"""
        return module_name in self.get_authorized_modules()
    
    def get_license_info(self) -> Optional[EnhancedLicenseInfo]:
        """获取许可证信息"""
        return self._license_info
    
    def get_license_status(self) -> LicenseStatus:
        """获取许可证状态"""
        return self._license_status

# 全局增强许可证管理器实例
enhanced_license_manager = EnhancedLicenseManager()
