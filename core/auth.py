from functools import wraps
from typing import Callable
import logging
from license.license_manager import license_manager, LicenseStatus

logger = logging.getLogger(__name__)

def require_license(module_id: str):
    """许可证权限装饰器（支持离线模式）"""
    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                # 加载许可证
                status, license_info = license_manager.load_license()

                # 检查许可证状态
                if status != LicenseStatus.VALID:
                    error_msg = license_manager.get_status_message()
                    logger.warning(f"许可证验证失败: {status.value}")

                    # 为不同的错误状态提供更详细的信息
                    if status == LicenseStatus.SECURITY_RISK:
                        security_report = license_manager.get_security_report()
                        logger.warning(f"安全报告: {security_report}")

                    raise PermissionError(f"许可证无效: {error_msg}")

                # 检查模块权限
                if not license_manager.is_module_authorized(module_id):
                    authorized_modules = license_manager.get_authorized_modules()
                    raise PermissionError(
                        f"模块 '{module_id}' 未被授权。\n"
                        f"当前授权模块: {', '.join(authorized_modules)}"
                    )

                # 增加执行计数
                if not license_manager.increment_execution_count():
                    raise PermissionError("已达到最大执行次数限制")

                logger.info(f"模块 '{module_id}' 执行授权成功")
                return func(*args, **kwargs)

            except PermissionError:
                raise
            except Exception as e:
                logger.error(f"许可证验证过程中发生错误: {e}")
                raise PermissionError(f"许可证验证失败: {str(e)}")

        return wrapper
    return decorator

class PermissionManager:
    """权限管理器（支持离线模式）"""

    @staticmethod
    def check_license_status() -> tuple:
        """检查许可证状态"""
        try:
            return license_manager.load_license()
        except Exception as e:
            logger.error(f"检查许可证状态失败: {e}")
            return LicenseStatus.INVALID, None

    @staticmethod
    def get_authorized_modules() -> list:
        """获取授权模块列表"""
        try:
            status, _ = license_manager.load_license()
            if status == LicenseStatus.VALID:
                return license_manager.get_authorized_modules()
            return []
        except Exception as e:
            logger.error(f"获取授权模块失败: {e}")
            return []

    @staticmethod
    def get_license_info():
        """获取许可证信息"""
        try:
            return license_manager.get_license_info()
        except Exception as e:
            logger.error(f"获取许可证信息失败: {e}")
            return None

    @staticmethod
    def get_status_message() -> str:
        """获取状态消息"""
        try:
            return license_manager.get_status_message()
        except Exception as e:
            logger.error(f"获取状态消息失败: {e}")
            return f"许可证状态检查失败: {str(e)}"

    @staticmethod
    def get_security_report() -> dict:
        """获取安全报告（离线模式）"""
        try:
            if hasattr(license_manager, 'get_security_report'):
                return license_manager.get_security_report()
            return {"status": "unknown", "message": "不支持安全报告"}
        except Exception as e:
            logger.error(f"获取安全报告失败: {e}")
            return {"status": "error", "message": str(e)}

    @staticmethod
    def is_offline_mode() -> bool:
        """检查是否为离线模式"""
        try:
            return getattr(license_manager, 'offline_mode', False)
        except Exception:
            return False