"""
许可证配置管理器
用于加载和管理许可证验证相关的配置
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class LicenseConfig:
    """许可证配置管理器"""
    
    def __init__(self, config_file: str = None):
        self.config_file = config_file or self._get_default_config_path()
        self._config: Optional[Dict[str, Any]] = None
        self._load_config()
    
    def _get_default_config_path(self) -> str:
        """获取默认配置文件路径"""
        config_dir = Path(__file__).parent.parent / "config"
        return str(config_dir / "license_config.json")
    
    def _load_config(self):
        """加载配置文件"""
        try:
            if Path(self.config_file).exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self._config = json.load(f)
                logger.info("许可证配置加载成功")
            else:
                logger.warning(f"配置文件不存在: {self.config_file}，使用默认配置")
                self._config = self._get_default_config()
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}，使用默认配置")
            self._config = self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "license_settings": {
                "offline_mode": True,
                "strict_mode": True
            },
            "security_settings": {
                "min_security_score": 70,
                "require_high_confidence": True,
                "max_time_jump_seconds": 300,
                "min_time_sources": 2,
                "max_source_deviation_seconds": 600
            },
            "time_validation": {
                "enable_ntp_check": True,
                "ntp_timeout_seconds": 3,
                "ntp_servers": [
                    "pool.ntp.org",
                    "time.nist.gov",
                    "time.google.com"
                ]
            },
            "crypto_settings": {
                "use_argon2": True,
                "pbkdf2_iterations": 200000,
                "argon2_time_cost": 3,
                "argon2_memory_cost": 65536,
                "argon2_parallelism": 1
            },
            "logging": {
                "log_license_attempts": True,
                "log_security_events": True,
                "log_level": "INFO"
            },
            "maintenance": {
                "auto_key_rotation": False,
                "key_rotation_days": 90,
                "cleanup_old_proofs": True,
                "max_proof_chain_length": 50
            }
        }
    
    def get(self, key_path: str, default=None):
        """获取配置值（支持点分隔的路径）"""
        try:
            keys = key_path.split('.')
            value = self._config
            
            for key in keys:
                if isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    return default
            
            return value
        except Exception as e:
            logger.debug(f"获取配置失败 {key_path}: {e}")
            return default
    
    def get_license_settings(self) -> Dict[str, Any]:
        """获取许可证设置"""
        return self.get('license_settings', {})
    
    def get_security_settings(self) -> Dict[str, Any]:
        """获取安全设置"""
        return self.get('security_settings', {})
    
    def get_time_validation_settings(self) -> Dict[str, Any]:
        """获取时间验证设置"""
        return self.get('time_validation', {})
    
    def get_crypto_settings(self) -> Dict[str, Any]:
        """获取加密设置"""
        return self.get('crypto_settings', {})
    
    def is_offline_mode(self) -> bool:
        """检查是否为离线模式"""
        return self.get('license_settings.offline_mode', True)
    
    def is_strict_mode(self) -> bool:
        """检查是否为严格模式"""
        return self.get('license_settings.strict_mode', True)
    
    def get_min_security_score(self) -> int:
        """获取最低安全评分"""
        return self.get('security_settings.min_security_score', 70)
    
    def reload(self):
        """重新加载配置"""
        self._load_config()

# 全局配置实例
license_config = LicenseConfig()
