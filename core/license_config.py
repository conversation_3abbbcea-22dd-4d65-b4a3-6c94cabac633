"""
安全许可证配置管理器
将关键安全参数硬编码，只允许用户配置非安全相关的参数
"""

import json
import hashlib
import hmac
import logging
from pathlib import Path
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class SecureLicenseConfig:
    """安全许可证配置管理器"""

    # 硬编码的核心安全参数（用户无法修改）
    CORE_SECURITY_PARAMS = {
        "min_security_score": 75,           # 最低安全评分（提高到75）
        "strict_mode_min_score": 85,        # 严格模式最低评分
        "max_time_jump_seconds": 180,       # 最大时间跳跃（降低到3分钟）
        "min_time_sources": 2,              # 最少时间源数量
        "max_source_deviation_seconds": 300, # 时间源最大偏差（5分钟）
        "require_high_confidence": True,     # 要求高置信度
        "offline_mode_default": True,        # 默认离线模式
        "strict_mode_default": True,         # 默认严格模式
        "max_anomaly_score": 25,            # 最大异常评分
        "min_anchor_score": 20,             # 最小锚点评分
        "min_integrity_score": 15,          # 最小完整性评分
        "crypto_iterations_min": 150000,    # 最小加密迭代次数
        "proof_chain_max_length": 50,       # 证明链最大长度
        "time_validation_timeout": 10,      # 时间验证超时（秒）
    }

    # 允许用户配置的参数（非安全相关）
    USER_CONFIGURABLE_PARAMS = {
        "logging_level",
        "ui_language",
        "show_detailed_warnings",
        "show_security_score",
        "cache_duration_minutes",
        "enable_fast_mode",
        "auto_cleanup_enabled",
        "cleanup_interval_hours",
        "keep_logs_days"
    }

    def __init__(self, config_file: str = None):
        self.config_file = config_file or self._get_default_config_path()
        self._user_config: Optional[Dict[str, Any]] = None
        self._load_user_config()
    
    def _get_default_config_path(self) -> str:
        """获取默认用户配置文件路径"""
        config_dir = Path(__file__).parent.parent / "config"
        return str(config_dir / "user_preferences.json")

    def _load_user_config(self):
        """加载用户配置文件（仅非安全参数）"""
        try:
            if Path(self.config_file).exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    raw_config = json.load(f)

                # 只保留允许用户配置的参数
                self._user_config = self._filter_user_config(raw_config)
                logger.info("用户配置加载成功")
            else:
                logger.info(f"用户配置文件不存在: {self.config_file}，使用默认设置")
                self._user_config = self._get_default_user_config()
        except Exception as e:
            logger.error(f"用户配置文件加载失败: {e}，使用默认设置")
            self._user_config = self._get_default_user_config()

    def _filter_user_config(self, raw_config: Dict[str, Any]) -> Dict[str, Any]:
        """过滤用户配置，只保留允许的参数"""
        filtered_config = {}

        def filter_recursive(source: Dict[str, Any], target: Dict[str, Any], path: str = ""):
            for key, value in source.items():
                current_path = f"{path}.{key}" if path else key

                if isinstance(value, dict):
                    target[key] = {}
                    filter_recursive(value, target[key], current_path)
                elif current_path in self.USER_CONFIGURABLE_PARAMS:
                    target[key] = value
                else:
                    # 记录被过滤的安全参数
                    if any(security_key in key.lower() for security_key in
                          ['security', 'score', 'time_jump', 'strict', 'min_', 'max_']):
                        logger.warning(f"安全参数 '{current_path}' 被忽略（硬编码保护）")

        filter_recursive(raw_config, filtered_config)
        return filtered_config

    def _get_default_user_config(self) -> Dict[str, Any]:
        """获取默认用户配置（仅非安全参数）"""
        return {
            "ui": {
                "language": "zh",
                "show_detailed_warnings": True,
                "show_security_score": True
            },
            "performance": {
                "cache_duration_minutes": 60,
                "enable_fast_mode": False
            },
            "maintenance": {
                "auto_cleanup_enabled": True,
                "cleanup_interval_hours": 168,
                "keep_logs_days": 30
            },
            "logging": {
                "level": "INFO"
            }
        }
    
    def get_security_param(self, param_name: str, default=None):
        """获取硬编码的安全参数"""
        return self.CORE_SECURITY_PARAMS.get(param_name, default)

    def get_user_param(self, key_path: str, default=None):
        """获取用户配置参数（支持点分隔的路径）"""
        try:
            keys = key_path.split('.')
            value = self._user_config

            for key in keys:
                if isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    return default

            return value
        except Exception as e:
            logger.debug(f"获取用户配置失败 {key_path}: {e}")
            return default

    def is_offline_mode(self) -> bool:
        """检查是否为离线模式（硬编码）"""
        return self.get_security_param("offline_mode_default", True)

    def is_strict_mode(self) -> bool:
        """检查是否为严格模式（硬编码）"""
        return self.get_security_param("strict_mode_default", True)

    def get_min_security_score(self, strict_mode: bool = None) -> int:
        """获取最低安全评分（硬编码）"""
        if strict_mode is None:
            strict_mode = self.is_strict_mode()

        if strict_mode:
            return self.get_security_param("strict_mode_min_score", 85)
        else:
            return self.get_security_param("min_security_score", 75)

    def get_max_time_jump(self) -> int:
        """获取最大时间跳跃（硬编码）"""
        return self.get_security_param("max_time_jump_seconds", 180)

    def get_min_time_sources(self) -> int:
        """获取最少时间源数量（硬编码）"""
        return self.get_security_param("min_time_sources", 2)

    def get_max_source_deviation(self) -> int:
        """获取时间源最大偏差（硬编码）"""
        return self.get_security_param("max_source_deviation_seconds", 300)

    def require_high_confidence(self) -> bool:
        """是否要求高置信度（硬编码）"""
        return self.get_security_param("require_high_confidence", True)

    def validate_config_integrity(self) -> bool:
        """验证配置完整性（防止篡改）"""
        try:
            # 检查关键安全参数是否被篡改
            expected_checksum = self._calculate_security_checksum()
            stored_checksum = getattr(self, '_security_checksum', None)

            if stored_checksum is None:
                self._security_checksum = expected_checksum
                return True

            return hmac.compare_digest(expected_checksum, stored_checksum)

        except Exception as e:
            logger.error(f"配置完整性验证失败: {e}")
            return False

    def _calculate_security_checksum(self) -> str:
        """计算安全参数校验和"""
        security_data = json.dumps(self.CORE_SECURITY_PARAMS, sort_keys=True)
        return hashlib.sha256(security_data.encode()).hexdigest()

    def get_all_security_params(self) -> Dict[str, Any]:
        """获取所有安全参数（只读）"""
        return self.CORE_SECURITY_PARAMS.copy()

    def reload_user_config(self):
        """重新加载用户配置"""
        self._load_user_config()

    def save_user_config(self, config: Dict[str, Any]):
        """保存用户配置（仅允许的参数）"""
        try:
            filtered_config = self._filter_user_config(config)

            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(filtered_config, f, indent=2, ensure_ascii=False)

            self._user_config = filtered_config
            logger.info("用户配置保存成功")

        except Exception as e:
            logger.error(f"保存用户配置失败: {e}")
            raise
    
# 向后兼容的旧接口（已弃用）
class LicenseConfig(SecureLicenseConfig):
    """向后兼容的配置类（已弃用，请使用SecureLicenseConfig）"""

    def __init__(self, config_file: str = None):
        logger.warning("LicenseConfig已弃用，请使用SecureLicenseConfig")
        super().__init__(config_file)

    def get(self, key_path: str, default=None):
        """获取配置值（向后兼容）"""
        # 尝试从安全参数获取
        if '.' in key_path:
            parts = key_path.split('.')
            if len(parts) == 2:
                section, param = parts
                if section == 'security_settings':
                    security_mapping = {
                        'min_security_score': 'min_security_score',
                        'require_high_confidence': 'require_high_confidence',
                        'max_time_jump_seconds': 'max_time_jump_seconds',
                        'min_time_sources': 'min_time_sources',
                        'max_source_deviation_seconds': 'max_source_deviation_seconds'
                    }
                    if param in security_mapping:
                        return self.get_security_param(security_mapping[param], default)
                elif section == 'license_settings':
                    if param == 'offline_mode':
                        return self.is_offline_mode()
                    elif param == 'strict_mode':
                        return self.is_strict_mode()

        # 从用户配置获取
        return self.get_user_param(key_path, default)

    def get_license_settings(self) -> Dict[str, Any]:
        """获取许可证设置（向后兼容）"""
        return {
            'offline_mode': self.is_offline_mode(),
            'strict_mode': self.is_strict_mode()
        }

    def get_security_settings(self) -> Dict[str, Any]:
        """获取安全设置（向后兼容）"""
        return {
            'min_security_score': self.get_min_security_score(),
            'require_high_confidence': self.require_high_confidence(),
            'max_time_jump_seconds': self.get_max_time_jump(),
            'min_time_sources': self.get_min_time_sources(),
            'max_source_deviation_seconds': self.get_max_source_deviation()
        }

# 全局配置实例（使用新的安全版本）
license_config = SecureLicenseConfig()
