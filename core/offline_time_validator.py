"""
离线时间验证器
专为无网络环境设计的安全时间验证系统
"""

import time
import hashlib
import hmac
import json
import os
import platform
import subprocess
import struct
import psutil
from datetime import datetime, timedelta
from typing import Optional, Tuple, Dict, List, Any
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class OfflineTimeValidationResult:
    """离线时间验证结果"""
    def __init__(self, is_valid: bool, current_time: datetime, 
                 confidence_level: str, security_score: int,
                 warnings: List[str] = None, evidence: Dict[str, Any] = None):
        self.is_valid = is_valid
        self.current_time = current_time
        self.confidence_level = confidence_level  # 'high', 'medium', 'low'
        self.security_score = security_score  # 0-100
        self.warnings = warnings or []
        self.evidence = evidence or {}  # 验证证据

class OfflineTimeValidator:
    """离线环境安全时间验证器"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        # 离线验证配置
        self.time_anchor_file = self.config_dir / ".time_anchors"
        self.time_proof_chain = self.config_dir / ".time_proof_chain"
        self.system_fingerprint_file = self.config_dir / ".system_fingerprint"
        
        # 从安全配置加载参数（硬编码保护）
        from core.license_config import license_config
        self.max_time_jump = license_config.get_max_time_jump()  # 3分钟（硬编码）
        self.min_security_score = license_config.get_min_security_score()  # 75分（硬编码）
        self.min_time_sources = license_config.get_min_time_sources()  # 2个源（硬编码）
        self.max_source_deviation = license_config.get_max_source_deviation()  # 5分钟（硬编码）

        # 离线环境标志
        self.offline_only_mode = True  # 强制离线模式，不尝试网络连接
        self.network_timeout = 1  # 网络检测超时（秒）
        
        # 初始化时间锚点
        self._initialize_time_anchors()
    
    def validate_license_time(self, start_date: str, end_date: str) -> OfflineTimeValidationResult:
        """
        离线环境下验证许可证时间有效性
        """
        warnings = []
        evidence = {}
        security_score = 0
        
        # 1. 获取多源时间信息
        time_sources = self._get_multiple_time_sources()
        evidence['time_sources'] = time_sources
        
        # 2. 选择最可信的时间
        trusted_time, time_confidence = self._select_trusted_time(time_sources)
        evidence['trusted_time'] = trusted_time.isoformat()
        evidence['time_confidence'] = time_confidence
        
        # 3. 检测时间异常
        anomaly_score = self._detect_time_anomalies(trusted_time, time_sources)
        evidence['anomaly_score'] = anomaly_score
        security_score += max(0, 30 - anomaly_score)  # 异常越少分数越高
        
        # 4. 验证时间锚点
        anchor_score = self._verify_time_anchors(trusted_time)
        evidence['anchor_score'] = anchor_score
        security_score += anchor_score
        
        # 5. 检查系统完整性
        integrity_score = self._check_system_integrity()
        evidence['integrity_score'] = integrity_score
        security_score += integrity_score
        
        # 6. 验证时间证明链并确保更新
        proof_score = self._verify_time_proof_chain(trusted_time)
        evidence['proof_score'] = proof_score
        security_score += proof_score
        
        # 7. 执行时间范围检查
        try:
            start_dt = datetime.fromisoformat(start_date)
            end_dt = datetime.fromisoformat(end_date)
        except ValueError as e:
            return OfflineTimeValidationResult(
                False, trusted_time, "low", 0,
                [f"日期格式错误: {e}"], evidence
            )
        
        is_valid = start_dt <= trusted_time <= end_dt
        
        # 8. 在严格模式下，增加额外的时间验证
        if self._is_strict_mode() and is_valid:
            # 检查时间是否过于接近过期
            days_until_expiry = (end_dt - trusted_time).days
            if days_until_expiry < 0:  # 已过期
                is_valid = False
            elif days_until_expiry < 1:  # 将在1天内过期
                warnings.append("许可证即将过期")
        
        # 9. 生成警告信息
        if security_score < self.min_security_score:
            warnings.append(f"安全评分过低: {security_score}/{100}")
        
        if anomaly_score > 20:
            warnings.append("检测到时间异常，可能存在篡改")
        
        if not is_valid:
            if trusted_time < start_dt:
                warnings.append(f"许可证尚未生效，生效时间: {start_date}")
            elif trusted_time > end_dt:
                warnings.append(f"许可证已过期，过期时间: {end_date}")
        
        # 10. 确定置信度
        if security_score >= 85:  # 提高置信度要求
            confidence = "high"
        elif security_score >= 70:
            confidence = "medium"
        else:
            confidence = "low"
        
        return OfflineTimeValidationResult(
            is_valid, trusted_time, confidence, security_score, warnings, evidence
        )

    def set_offline_only_mode(self, enabled: bool = True):
        """设置完全离线模式（不尝试任何网络连接）"""
        self.offline_only_mode = enabled
        if enabled:
            logger.info("启用完全离线模式：将跳过所有网络时间检查")
        else:
            logger.info("禁用完全离线模式：允许网络时间检查")

    def is_offline_only_mode(self) -> bool:
        """检查是否为完全离线模式"""
        return self.offline_only_mode

    def _get_multiple_time_sources(self) -> Dict[str, datetime]:
        """获取多个本地时间源（增强版）"""
        sources = {}

        # 1. 系统时间
        sources['system'] = datetime.now()

        # 2. 硬件时钟时间 (BIOS/UEFI)
        try:
            hw_time = self._get_hardware_clock()
            if hw_time:
                sources['hardware'] = hw_time
        except Exception as e:
            logger.debug(f"无法读取硬件时钟: {e}")

        # 3. 文件系统时间
        try:
            fs_time = self._get_filesystem_time()
            if fs_time:
                sources['filesystem'] = fs_time
        except Exception as e:
            logger.debug(f"无法读取文件系统时间: {e}")

        # 4. 进程启动时间推算
        try:
            process_time = self._get_process_time()
            if process_time:
                sources['process'] = process_time
        except Exception as e:
            logger.debug(f"无法计算进程时间: {e}")

        # 5. 网络时间检查（仅在明确允许时）
        if not self.offline_only_mode:
            try:
                ntp_time = self._get_ntp_time_safe()
                if ntp_time:
                    sources['ntp'] = ntp_time
                    logger.info("成功获取网络时间")
            except Exception as e:
                logger.debug(f"网络时间获取失败（正常，离线环境）: {e}")
        else:
            logger.debug("离线模式：跳过网络时间检查")

        # 6. 多个文件系统时间戳
        try:
            fs_times = self._get_multiple_filesystem_times()
            for i, fs_time in enumerate(fs_times):
                sources[f'filesystem_{i}'] = fs_time
        except Exception as e:
            logger.debug(f"多文件系统时间获取失败: {e}")

        return sources
    
    def _get_hardware_clock(self) -> Optional[datetime]:
        """读取硬件时钟时间"""
        try:
            if platform.system() == "Windows":
                # Windows: 使用 wmic 命令
                result = subprocess.run(
                    ['wmic', 'bios', 'get', 'releasedate', '/value'],
                    capture_output=True, text=True, timeout=5
                )
                # 这里简化处理，实际可以读取更精确的硬件时间
                return None  # Windows硬件时钟读取较复杂
            
            elif platform.system() == "Linux":
                # Linux: 读取 /sys/class/rtc/rtc0/time
                rtc_path = Path("/sys/class/rtc/rtc0/time")
                if rtc_path.exists():
                    with open(rtc_path, 'r') as f:
                        rtc_time = f.read().strip()
                    # 解析RTC时间格式
                    return datetime.fromtimestamp(int(rtc_time))
            
            elif platform.system() == "Darwin":
                # macOS: 使用 hwclock 或类似命令
                result = subprocess.run(
                    ['date'], capture_output=True, text=True, timeout=5
                )
                # 简化处理
                return None
                
        except Exception as e:
            logger.debug(f"硬件时钟读取失败: {e}")
        
        return None
    
    def _get_filesystem_time(self) -> Optional[datetime]:
        """从文件系统获取时间参考"""
        try:
            # 使用系统关键文件的时间戳作为参考
            reference_files = [
                "/etc/passwd",  # Linux
                "/System/Library/CoreServices/SystemVersion.plist",  # macOS
                "C:\\Windows\\System32\\kernel32.dll",  # Windows
            ]
            
            for file_path in reference_files:
                if os.path.exists(file_path):
                    stat = os.stat(file_path)
                    # 使用修改时间作为参考
                    return datetime.fromtimestamp(stat.st_mtime)
            
            # 如果没有找到系统文件，使用当前目录
            current_dir = Path(".")
            if current_dir.exists():
                stat = current_dir.stat()
                return datetime.fromtimestamp(stat.st_mtime)
                
        except Exception as e:
            logger.debug(f"文件系统时间获取失败: {e}")
        
        return None
    
    def _get_process_time(self) -> Optional[datetime]:
        """通过进程运行时间推算当前时间"""
        try:
            # 获取当前进程信息
            current_process = psutil.Process()
            create_time = current_process.create_time()
            
            # 计算进程运行时间
            process_start = datetime.fromtimestamp(create_time)
            
            # 这里可以结合其他信息来推算更准确的时间
            # 简化处理：假设进程刚启动
            return process_start + timedelta(seconds=time.time() - create_time)
            
        except Exception as e:
            logger.debug(f"进程时间计算失败: {e}")
        
        return None

    def _get_ntp_time_safe(self) -> Optional[datetime]:
        """安全获取网络时间（快速失败，适合离线环境）"""
        if self.offline_only_mode:
            return None

        try:
            # 首先快速检测网络连接
            if not self._check_network_connectivity():
                logger.debug("网络连接不可用，跳过NTP检查")
                return None

            import socket
            import struct

            # 使用更短的超时时间，快速失败
            ntp_servers = [
                '*******',  # Google DNS（更可靠）
                'time.nist.gov',
                'pool.ntp.org'
            ]

            for server in ntp_servers:
                try:
                    # 创建NTP请求包
                    client = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                    client.settimeout(self.network_timeout)  # 1秒超时

                    # NTP请求数据
                    data = b'\x1b' + 47 * b'\0'
                    client.sendto(data, (server, 123))

                    # 接收响应
                    response, _ = client.recvfrom(1024)
                    client.close()

                    if len(response) >= 48:
                        # 解析NTP时间戳
                        timestamp = struct.unpack('!I', response[40:44])[0]
                        # NTP时间从1900年开始，Unix时间从1970年开始
                        unix_timestamp = timestamp - 2208988800
                        return datetime.fromtimestamp(unix_timestamp)

                except Exception as e:
                    logger.debug(f"NTP服务器 {server} 连接失败: {e}")
                    continue

            return None

        except Exception as e:
            logger.debug(f"网络时间获取失败: {e}")
            return None

    def _check_network_connectivity(self) -> bool:
        """快速检测网络连接（不阻塞）"""
        try:
            import socket

            # 尝试连接到可靠的DNS服务器
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.network_timeout)
            result = sock.connect_ex(('*******', 53))
            sock.close()

            return result == 0

        except Exception:
            return False

    def _get_multiple_filesystem_times(self) -> List[datetime]:
        """获取多个文件系统时间戳"""
        times = []

        try:
            # 系统关键文件列表（扩展版）
            reference_files = [
                # Linux
                "/etc/passwd", "/etc/hosts", "/proc/version", "/sys/kernel/notes",
                # macOS
                "/System/Library/CoreServices/SystemVersion.plist",
                "/usr/bin/uname", "/Library/Preferences/.GlobalPreferences.plist",
                # Windows
                "C:\\Windows\\System32\\kernel32.dll",
                "C:\\Windows\\System32\\ntdll.dll",
                "C:\\Windows\\System32\\user32.dll",
                # 通用
                os.path.expanduser("~"), "."
            ]

            for file_path in reference_files:
                try:
                    if os.path.exists(file_path):
                        stat = os.stat(file_path)
                        # 使用修改时间
                        times.append(datetime.fromtimestamp(stat.st_mtime))

                        # 如果有足够的时间戳就停止
                        if len(times) >= 5:
                            break

                except Exception as e:
                    logger.debug(f"文件时间戳获取失败 {file_path}: {e}")
                    continue

            return times

        except Exception as e:
            logger.debug(f"多文件系统时间获取失败: {e}")
            return []

    def _select_trusted_time(self, time_sources: Dict[str, datetime]) -> Tuple[datetime, str]:
        """从多个时间源中选择最可信的时间（增强版）"""
        if not time_sources:
            return datetime.now(), "low"

        # 如果只有一个时间源
        if len(time_sources) == 1:
            source_name = list(time_sources.keys())[0]
            confidence = "medium" if source_name == "ntp" else "low"
            return list(time_sources.values())[0], confidence

        # 检查是否有足够的时间源（离线环境下放宽要求）
        if len(time_sources) < self.min_time_sources:
            if self.offline_only_mode and len(time_sources) >= 1:
                logger.info(f"离线模式：时间源数量 {len(time_sources)} 少于推荐值 {self.min_time_sources}，但可接受")
            else:
                logger.warning(f"时间源数量不足: {len(time_sources)} < {self.min_time_sources}")

        # 时间源权重（离线环境优化：硬件时钟 > 系统时间 > 进程时间 > 文件系统时间）
        source_weights = {
            'ntp': 12,          # 网络时间（如果可用）
            'hardware': 10,     # 硬件时钟（离线环境最可靠）
            'system': 8,        # 系统时间
            'process': 6,       # 进程时间
            'filesystem': 4     # 文件系统时间
        }

        # 为文件系统时间源分配权重
        for key in time_sources.keys():
            if key.startswith('filesystem_'):
                source_weights[key] = 2

        # 计算加权平均时间
        weighted_times = []
        total_weight = 0

        times = list(time_sources.values())
        timestamps = [t.timestamp() for t in times]

        # 检查时间源之间的差异
        max_diff = max(timestamps) - min(timestamps)

        if max_diff > self.max_source_deviation:
            logger.warning(f"时间源差异过大: {max_diff}秒 > {self.max_source_deviation}秒")
            # 移除异常值
            time_sources = self._remove_time_outliers(time_sources)
            if not time_sources:
                return datetime.now(), "low"

        # 重新计算
        for source_name, time_value in time_sources.items():
            weight = source_weights.get(source_name, 1)
            # 如果是文件系统时间源的通用匹配
            if source_name.startswith('filesystem') and source_name not in source_weights:
                weight = 2

            weighted_times.append(time_value.timestamp() * weight)
            total_weight += weight

        if total_weight == 0:
            return datetime.now(), "low"

        # 计算加权平均时间
        avg_timestamp = sum(weighted_times) / total_weight
        trusted_time = datetime.fromtimestamp(avg_timestamp)

        # 确定置信度
        confidence = self._calculate_time_confidence(time_sources, max_diff)

        return trusted_time, confidence

    def _remove_time_outliers(self, time_sources: Dict[str, datetime]) -> Dict[str, datetime]:
        """移除时间异常值"""
        if len(time_sources) <= 2:
            return time_sources

        timestamps = [t.timestamp() for t in time_sources.values()]
        mean_time = sum(timestamps) / len(timestamps)

        # 计算标准差
        variance = sum((t - mean_time) ** 2 for t in timestamps) / len(timestamps)
        std_dev = variance ** 0.5

        # 移除超过2个标准差的异常值
        filtered_sources = {}
        for name, time_value in time_sources.items():
            if abs(time_value.timestamp() - mean_time) <= 2 * std_dev:
                filtered_sources[name] = time_value

        return filtered_sources if filtered_sources else time_sources

    def _calculate_time_confidence(self, time_sources: Dict[str, datetime], max_diff: float) -> str:
        """计算时间验证置信度（离线环境优化）"""
        # 基础评分
        score = 0

        # 时间源数量评分（离线环境下调整）
        if len(time_sources) >= 4:
            score += 35
        elif len(time_sources) >= 3:
            score += 25
        elif len(time_sources) >= 2:
            score += 15
        elif len(time_sources) >= 1 and self.offline_only_mode:
            score += 10  # 离线环境下单一时间源也给予基础分

        # 时间源质量评分（离线环境优化）
        if 'ntp' in time_sources:
            score += 30  # 网络时间最高分
        if 'hardware' in time_sources:
            score += 25  # 硬件时钟在离线环境下很重要
        if 'system' in time_sources:
            score += 15
        if 'process' in time_sources:
            score += 10

        # 文件系统时间源评分
        fs_sources = [k for k in time_sources.keys() if k.startswith('filesystem')]
        if fs_sources:
            score += min(len(fs_sources) * 3, 15)  # 多个文件系统时间源

        # 时间一致性评分（更严格）
        if max_diff <= 10:  # 10秒内
            score += 30
        elif max_diff <= 30:  # 30秒内
            score += 25
        elif max_diff <= 60:  # 1分钟内
            score += 20
        elif max_diff <= 180:  # 3分钟内
            score += 10
        elif max_diff <= 300:  # 5分钟内
            score += 5

        # 离线环境下的特殊加分
        if self.offline_only_mode:
            # 如果有硬件时钟和系统时间，给予额外加分
            if 'hardware' in time_sources and 'system' in time_sources:
                score += 10
            # 如果有多个文件系统时间源，给予额外加分
            if len(fs_sources) >= 2:
                score += 5

        # 确定置信度等级（离线环境下调整阈值）
        if self.offline_only_mode:
            # 离线环境下降低阈值
            if score >= 60:
                return "high"
            elif score >= 35:
                return "medium"
            else:
                return "low"
        else:
            # 在线环境下保持严格标准
            if score >= 75:
                return "high"
            elif score >= 45:
                return "medium"
            else:
                return "low"
    
    def _detect_time_anomalies(self, current_time: datetime,
                             time_sources: Dict[str, datetime]) -> int:
        """检测时间异常，返回异常评分 (0-100，越高越异常) - 增强版"""
        anomaly_score = 0

        # 1. 检查时间源之间的差异（更严格的阈值）
        if len(time_sources) > 1:
            times = list(time_sources.values())
            timestamps = [t.timestamp() for t in times]
            max_diff = max(timestamps) - min(timestamps)

            if max_diff > 1800:  # 30分钟（从1小时降低）
                anomaly_score += 50
            elif max_diff > 600:  # 10分钟（从5分钟提高）
                anomaly_score += 30
            elif max_diff > 300:  # 5分钟
                anomaly_score += 20
            elif max_diff > 60:  # 1分钟
                anomaly_score += 10

        # 2. 检查时间跳跃（更严格）
        last_time = self._get_last_recorded_time()
        if last_time:
            time_diff = abs((current_time - last_time).total_seconds())
            expected_diff = time.time() - self._get_last_record_timestamp()

            jump_diff = abs(time_diff - expected_diff)
            if jump_diff > self.max_time_jump:
                # 根据跳跃程度分级评分
                if jump_diff > 3600:  # 超过1小时
                    anomaly_score += 40
                elif jump_diff > 1800:  # 超过30分钟
                    anomaly_score += 35
                else:
                    anomaly_score += 30

        # 3. 检查系统启动时间一致性（增强）
        try:
            boot_time = datetime.fromtimestamp(psutil.boot_time())
            uptime = (current_time - boot_time).total_seconds()

            # 更严格的运行时间检查
            if uptime < 0:  # 时间回滚
                anomaly_score += 40
            elif uptime > 365 * 24 * 3600:  # 超过1年
                anomaly_score += 25
            elif uptime < 10:  # 系统运行时间过短（可能重启后时间被修改）
                anomaly_score += 15

        except Exception as e:
            logger.debug(f"系统启动时间检查失败: {e}")
            anomaly_score += 10  # 无法获取启动时间也是异常

        # 4. 检查时间变化率异常
        try:
            time_change_rate = self._check_time_change_rate(current_time)
            if time_change_rate > 1.1:  # 时间流逝过快
                anomaly_score += 20
            elif time_change_rate < 0.9:  # 时间流逝过慢
                anomaly_score += 15
        except Exception as e:
            logger.debug(f"时间变化率检查失败: {e}")

        # 5. 检查特定时间源的可信度
        if 'ntp' in time_sources and 'system' in time_sources:
            ntp_system_diff = abs(
                time_sources['ntp'].timestamp() - time_sources['system'].timestamp()
            )
            if ntp_system_diff > 300:  # NTP与系统时间差异超过5分钟
                anomaly_score += 25

        return min(anomaly_score, 100)

    def _check_time_change_rate(self, current_time: datetime) -> float:
        """检查时间变化率（检测时间加速/减速）"""
        try:
            # 获取上次记录的时间和实际时间戳
            last_recorded = self._get_last_recorded_time()
            last_timestamp = self._get_last_record_timestamp()

            if not last_recorded:
                return 1.0  # 无历史数据，假设正常

            # 计算时间差
            logical_diff = (current_time - last_recorded).total_seconds()
            actual_diff = time.time() - last_timestamp

            if actual_diff <= 0:
                return 1.0  # 避免除零错误

            # 计算时间变化率
            rate = logical_diff / actual_diff
            return rate

        except Exception as e:
            logger.debug(f"时间变化率计算失败: {e}")
            return 1.0
    
    def _initialize_time_anchors(self):
        """初始化时间锚点"""
        if not self.time_anchor_file.exists():
            anchors = {
                'creation_time': datetime.now().isoformat(),
                'system_info': self._get_system_fingerprint(),
                'anchor_points': []
            }
            self._save_secure_data(self.time_anchor_file, anchors)
    
    def _verify_time_anchors(self, current_time: datetime) -> int:
        """验证时间锚点，返回可信度评分 (0-30)"""
        try:
            anchors = self._load_secure_data(self.time_anchor_file)
            if not anchors:
                return 0
            
            creation_time = datetime.fromisoformat(anchors['creation_time'])
            
            # 检查时间单调性
            if current_time < creation_time:
                logger.warning("检测到时间回滚到锚点创建之前")
                return 0
            
            # 检查时间合理性
            time_diff = (current_time - creation_time).total_seconds()
            if time_diff > 365 * 24 * 3600:  # 超过1年
                logger.warning("时间锚点过于久远")
                return 10
            
            return 30
            
        except Exception as e:
            logger.error(f"时间锚点验证失败: {e}")
            return 0

    def _check_system_integrity(self) -> int:
        """检查系统完整性，返回评分 (0-20)"""
        try:
            current_fingerprint = self._get_system_fingerprint()
            stored_fingerprint = self._load_secure_data(self.system_fingerprint_file)

            if not stored_fingerprint:
                # 首次运行，保存指纹
                self._save_secure_data(self.system_fingerprint_file, current_fingerprint)
                return 15

            # 比较系统指纹
            similarity = self._compare_fingerprints(current_fingerprint, stored_fingerprint)

            # 提高相似度阈值要求，增强安全性
            if similarity > 0.95:
                return 20
            elif similarity > 0.85:
                return 15
            elif similarity > 0.75:
                return 10
            else:
                logger.warning("系统环境发生重大变化")
                return 0

        except Exception as e:
            logger.error(f"系统完整性检查失败: {e}")
            return 0

    def _verify_time_proof_chain(self, current_time: datetime) -> int:
        """验证时间证明链，返回评分 (0-20)"""
        try:
            proof_chain = self._load_secure_data(self.time_proof_chain)

            # 总是添加新的时间证明到链中，即使链不存在
            new_proof = self._create_time_proof(current_time)
            
            if not proof_chain:
                # 创建新的证明链
                self._save_secure_data(self.time_proof_chain, [new_proof])
                return 10

            # 验证证明链的完整性
            if self._validate_proof_chain(proof_chain, current_time):
                # 添加新的证明到链中
                proof_chain.append(new_proof)

                # 保持链的长度在合理范围内
                if len(proof_chain) > 100:
                    proof_chain = proof_chain[-50:]  # 保留最近50个证明

                self._save_secure_data(self.time_proof_chain, proof_chain)
                return 20
            else:
                logger.warning("时间证明链验证失败")
                # 即使验证失败，也保存新的证明以供调试
                self._save_secure_data(self.time_proof_chain, [new_proof])
                return 0

        except Exception as e:
            logger.error(f"时间证明链验证失败: {e}")
            return 0

    def _validate_license_time_and_update(self, start_date: str, end_date: str) -> OfflineTimeValidationResult:
        """
        验证许可证时间并更新相关文件
        这个方法确保在每次验证时都更新时间相关文件
        """
        # 先执行正常的验证
        result = self.validate_license_time(start_date, end_date)
        
        # 更新系统指纹（如果不存在）
        try:
            if not self.system_fingerprint_file.exists():
                current_fingerprint = self._get_system_fingerprint()
                self._save_secure_data(self.system_fingerprint_file, current_fingerprint)
        except Exception as e:
            logger.warning(f"更新系统指纹失败: {e}")
        
        # 更新时间锚点（如果不存在）
        try:
            self._initialize_time_anchors()
        except Exception as e:
            logger.warning(f"更新时间锚点失败: {e}")
        
        return result

    def _get_system_fingerprint(self) -> Dict[str, Any]:
        """获取系统指纹"""
        fingerprint = {}

        try:
            # 基本系统信息
            fingerprint['platform'] = platform.platform()
            fingerprint['processor'] = platform.processor()
            fingerprint['machine'] = platform.machine()

            # CPU信息
            fingerprint['cpu_count'] = psutil.cpu_count()
            fingerprint['cpu_freq'] = psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None

            # 内存信息
            memory = psutil.virtual_memory()
            fingerprint['memory_total'] = memory.total

            # 磁盘信息
            disk_usage = psutil.disk_usage('/')
            fingerprint['disk_total'] = disk_usage.total

            # 网络接口（不包含IP，只包含MAC地址）
            network_interfaces = []
            for interface, addrs in psutil.net_if_addrs().items():
                for addr in addrs:
                    if addr.family == psutil.AF_LINK:  # MAC地址
                        network_interfaces.append({
                            'interface': interface,
                            'mac': addr.address
                        })
            fingerprint['network_interfaces'] = network_interfaces

        except Exception as e:
            logger.debug(f"获取系统指纹部分失败: {e}")

        return fingerprint

    def _compare_fingerprints(self, fp1: Dict[str, Any], fp2: Dict[str, Any]) -> float:
        """比较两个系统指纹的相似度"""
        if not fp1 or not fp2:
            return 0.0

        total_score = 0
        max_score = 0

        # 比较各个字段
        fields_weights = {
            'platform': 0.2,
            'processor': 0.2,
            'machine': 0.1,
            'cpu_count': 0.1,
            'memory_total': 0.1,
            'disk_total': 0.1,
            'network_interfaces': 0.2
        }

        for field, weight in fields_weights.items():
            max_score += weight

            if field in fp1 and field in fp2:
                if field == 'network_interfaces':
                    # 特殊处理网络接口
                    if self._compare_network_interfaces(fp1[field], fp2[field]):
                        total_score += weight
                    else:
                        # 网络接口不匹配时，如果是严格模式则直接返回低分
                        if self._is_strict_mode():
                            return 0.0
                elif fp1[field] == fp2[field]:
                    total_score += weight
                elif field in ['memory_total', 'disk_total']:
                    # 内存和磁盘允许一定差异，但限制更严格
                    diff_ratio = abs(fp1[field] - fp2[field]) / max(fp1[field], fp2[field])
                    if diff_ratio < 0.05:  # 降低到5%的差异容忍度
                        total_score += weight * 0.9
                    elif diff_ratio < 0.1:
                        total_score += weight * 0.7
                    else:
                        # 差异过大，严格模式下直接返回低分
                        if self._is_strict_mode():
                            return 0.0

        return total_score / max_score if max_score > 0 else 0.0

    def _is_strict_mode(self) -> bool:
        """检查是否为严格模式"""
        try:
            # 尝试从安全配置加载严格模式设置
            from .secure_config_manager import secure_config_manager
            config = secure_config_manager.load_secure_config()
            offline_security = config.get("offline_security", {})
            return offline_security.get("strict_mode", True)
        except:
            # 默认为严格模式
            return True

    def _compare_network_interfaces(self, interfaces1: List[Dict], interfaces2: List[Dict]) -> bool:
        """比较网络接口"""
        if not interfaces1 or not interfaces2:
            return False

        macs1 = {iface['mac'] for iface in interfaces1}
        macs2 = {iface['mac'] for iface in interfaces2}

        # 至少有一个MAC地址匹配
        return len(macs1.intersection(macs2)) > 0

    def _create_time_proof(self, current_time: datetime) -> Dict[str, Any]:
        """创建时间证明"""
        proof = {
            'timestamp': current_time.isoformat(),
            'system_uptime': time.time() - psutil.boot_time(),
            'process_id': os.getpid(),
            'random_nonce': os.urandom(16).hex(),
            'system_hash': self._get_system_state_hash()
        }

        # 生成证明签名
        proof_data = json.dumps(proof, sort_keys=True)
        signature = hmac.new(
            self._get_signing_key(),
            proof_data.encode(),
            hashlib.sha256
        ).hexdigest()

        proof['signature'] = signature
        return proof

    def _validate_proof_chain(self, proof_chain: List[Dict], current_time: datetime) -> bool:
        """验证时间证明链"""
        if not proof_chain:
            return True

        # 验证每个证明的签名
        for proof in proof_chain:
            if not self._verify_proof_signature(proof):
                return False

        # 验证时间单调性
        for i in range(1, len(proof_chain)):
            prev_time = datetime.fromisoformat(proof_chain[i-1]['timestamp'])
            curr_time = datetime.fromisoformat(proof_chain[i]['timestamp'])

            if curr_time < prev_time:
                logger.warning("检测到时间证明链中的时间回滚")
                return False

        # 验证与当前时间的一致性
        if proof_chain:
            last_proof_time = datetime.fromisoformat(proof_chain[-1]['timestamp'])
            if current_time < last_proof_time:
                logger.warning("当前时间早于最后一个时间证明")
                return False

        return True

    def _verify_proof_signature(self, proof: Dict[str, Any]) -> bool:
        """验证时间证明签名"""
        try:
            signature = proof.pop('signature')
            proof_data = json.dumps(proof, sort_keys=True)

            expected_signature = hmac.new(
                self._get_signing_key(),
                proof_data.encode(),
                hashlib.sha256
            ).hexdigest()

            proof['signature'] = signature  # 恢复签名

            return hmac.compare_digest(signature, expected_signature)

        except Exception as e:
            logger.error(f"时间证明签名验证失败: {e}")
            return False

    def _get_system_state_hash(self) -> str:
        """获取系统状态哈希"""
        try:
            # 收集系统状态信息
            state_info = {
                'boot_time': psutil.boot_time(),
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_usage': psutil.disk_usage('/').percent,
                'process_count': len(psutil.pids())
            }

            state_data = json.dumps(state_info, sort_keys=True)
            return hashlib.sha256(state_data.encode()).hexdigest()[:16]

        except Exception:
            return "unknown"

    def _get_signing_key(self) -> bytes:
        """获取签名密钥"""
        # 基于系统特征生成一致的密钥
        fingerprint = self._get_system_fingerprint()
        key_data = json.dumps(fingerprint, sort_keys=True)
        return hashlib.sha256(key_data.encode()).digest()

    def _save_secure_data(self, file_path: Path, data: Any):
        """安全保存数据"""
        try:
            json_data = json.dumps(data, ensure_ascii=False, indent=2)

            # 生成数据哈希
            data_hash = hashlib.sha256(json_data.encode()).hexdigest()

            # 保存数据和哈希
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(json_data + '\n' + data_hash)

        except Exception as e:
            logger.error(f"保存安全数据失败: {e}")

    def _load_secure_data(self, file_path: Path) -> Any:
        """安全加载数据"""
        try:
            if not file_path.exists():
                return None

            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()

            lines = content.split('\n')
            if len(lines) < 2:
                return None

            json_data = '\n'.join(lines[:-1])
            stored_hash = lines[-1]

            # 验证数据完整性
            calculated_hash = hashlib.sha256(json_data.encode()).hexdigest()
            if calculated_hash != stored_hash:
                logger.warning(f"数据完整性验证失败: {file_path}")
                return None

            return json.loads(json_data)

        except Exception as e:
            logger.error(f"加载安全数据失败: {e}")
            return None

    def _get_last_recorded_time(self) -> Optional[datetime]:
        """获取最后记录的时间"""
        try:
            proof_chain = self._load_secure_data(self.time_proof_chain)
            if proof_chain and len(proof_chain) > 0:
                return datetime.fromisoformat(proof_chain[-1]['timestamp'])
        except:
            pass
        return None

    def _get_last_record_timestamp(self) -> float:
        """获取最后记录的时间戳"""
        try:
            if self.time_proof_chain.exists():
                return self.time_proof_chain.stat().st_mtime
        except:
            pass
        return time.time()

# 全局离线时间验证器实例
offline_time_validator = OfflineTimeValidator()
