#!/usr/bin/env python3
"""
离线许可证系统测试脚本
用于验证安全改进后的许可证验证功能
"""

import sys
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_crypto_manager():
    """测试加密管理器"""
    print("=== 测试加密管理器 ===")
    
    try:
        from license.crypto_utils import crypto_manager
        
        # 测试数据加密/解密
        test_data = {
            "test": "data",
            "number": 123,
            "list": [1, 2, 3]
        }
        
        print("1. 测试数据加密/解密...")
        encrypted = crypto_manager.encrypt_data(test_data)
        print(f"   加密成功，长度: {len(encrypted)}")
        
        decrypted = crypto_manager.decrypt_data(encrypted)
        print(f"   解密成功: {decrypted == test_data}")
        
        # 测试签名生成/验证
        print("2. 测试签名生成/验证...")
        signature = crypto_manager.generate_signature(test_data)
        print(f"   签名生成成功，长度: {len(signature)}")
        
        is_valid = crypto_manager.verify_signature(test_data, signature)
        print(f"   签名验证成功: {is_valid}")
        
        # 测试篡改检测
        tampered_data = test_data.copy()
        tampered_data["test"] = "tampered"
        is_tampered = crypto_manager.verify_signature(tampered_data, signature)
        print(f"   篡改检测成功: {not is_tampered}")
        
        print("✓ 加密管理器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 加密管理器测试失败: {e}")
        return False

def test_time_validator():
    """测试时间验证器"""
    print("\n=== 测试时间验证器 ===")
    
    try:
        from core.offline_time_validator import offline_time_validator
        from datetime import datetime, timedelta
        
        # 测试时间验证
        print("1. 测试时间验证...")
        start_date = (datetime.now() - timedelta(days=1)).isoformat()
        end_date = (datetime.now() + timedelta(days=30)).isoformat()
        
        result = offline_time_validator.validate_license_time(start_date, end_date)
        print(f"   时间验证结果: {result.is_valid}")
        print(f"   安全评分: {result.security_score}/100")
        print(f"   置信度: {result.confidence_level}")
        print(f"   警告数量: {len(result.warnings)}")
        
        if result.warnings:
            print("   警告信息:")
            for warning in result.warnings:
                print(f"     - {warning}")
        
        print("✓ 时间验证器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 时间验证器测试失败: {e}")
        return False

def test_license_manager():
    """测试许可证管理器"""
    print("\n=== 测试许可证管理器 ===")
    
    try:
        from license.license_manager import LicenseManager, LicenseStatus
        
        # 创建许可证管理器实例
        print("1. 创建许可证管理器...")
        manager = LicenseManager(offline_mode=True, strict_mode=True)
        print(f"   离线模式: {manager.offline_mode}")
        print(f"   严格模式: {manager.strict_mode}")
        print(f"   最低安全评分: {manager.min_security_score}")
        
        # 尝试加载许可证
        print("2. 尝试加载许可证...")
        status, license_info = manager.load_license()
        print(f"   许可证状态: {status.value}")
        
        if status == LicenseStatus.VALID:
            print(f"   组织: {license_info.organization}")
            print(f"   用户: {license_info.user_id}")
            print(f"   模块: {', '.join(license_info.modules)}")
            print(f"   执行次数: {license_info.current_executions}/{license_info.max_executions}")
        
        # 获取状态消息
        print("3. 获取状态消息...")
        status_msg = manager.get_status_message()
        print(f"   状态消息: {status_msg}")
        
        # 获取安全报告
        if manager.offline_mode:
            print("4. 获取安全报告...")
            security_report = manager.get_security_report()
            print(f"   安全报告状态: {security_report.get('status', 'unknown')}")
            if 'security_score' in security_report:
                print(f"   安全评分: {security_report['security_score']}/100")
                print(f"   置信度: {security_report['confidence_level']}")
        
        print("✓ 许可证管理器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 许可证管理器测试失败: {e}")
        return False

def test_config_system():
    """测试配置系统"""
    print("\n=== 测试配置系统 ===")
    
    try:
        from core.license_config import license_config
        
        print("1. 测试配置加载...")
        print(f"   离线模式: {license_config.is_offline_mode()}")
        print(f"   严格模式: {license_config.is_strict_mode()}")
        print(f"   最低安全评分: {license_config.get_min_security_score()}")
        
        print("2. 测试配置获取...")
        security_settings = license_config.get_security_settings()
        print(f"   安全设置项数: {len(security_settings)}")
        
        time_settings = license_config.get_time_validation_settings()
        print(f"   时间验证设置项数: {len(time_settings)}")
        
        print("✓ 配置系统测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 配置系统测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始离线许可证系统测试...\n")
    
    tests = [
        test_crypto_manager,
        test_time_validator,
        test_license_manager,
        test_config_system
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"测试执行异常: {e}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return 0
    else:
        print("❌ 部分测试失败")
        return 1

if __name__ == '__main__':
    sys.exit(main())
