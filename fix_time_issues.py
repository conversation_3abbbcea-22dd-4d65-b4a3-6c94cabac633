#!/usr/bin/env python3
"""
修复时间验证问题
清理所有时间相关的缓存和锚点文件
"""

import os
import glob
from pathlib import Path

def clean_time_files():
    """清理所有时间相关文件"""
    print("🧹 清理时间相关文件...")
    
    # 要清理的文件模式
    patterns = [
        "config/.time_anchors*",
        "config/.crypto_keyinfo*",
        "config/.security_policy*",
        "**/.time_*",
        "**/.crypto_*",
        "**/time_anchor*",
        "**/crypto_key*"
    ]
    
    cleaned_files = []
    
    for pattern in patterns:
        files = glob.glob(pattern, recursive=True)
        for file_path in files:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    cleaned_files.append(file_path)
                    print(f"   ✅ 删除: {file_path}")
            except Exception as e:
                print(f"   ❌ 删除失败 {file_path}: {e}")
    
    if not cleaned_files:
        print("   ℹ️ 没有找到需要清理的文件")
    else:
        print(f"   🎯 总共清理了 {len(cleaned_files)} 个文件")

def disable_strict_time_validation():
    """临时禁用严格的时间验证"""
    print("\n⚙️ 调整时间验证设置...")
    
    try:
        # 修改时间验证器的严格模式
        from core.offline_time_validator import offline_time_validator
        
        # 临时放宽时间验证参数
        offline_time_validator.max_time_jump = 86400  # 24小时
        offline_time_validator.max_source_deviation = 86400  # 24小时
        offline_time_validator.min_security_score = 30  # 降低最低评分
        
        print("   ✅ 时间验证参数已临时放宽")
        print(f"   - 最大时间跳跃: {offline_time_validator.max_time_jump}秒")
        print(f"   - 最大源偏差: {offline_time_validator.max_source_deviation}秒")
        print(f"   - 最低安全评分: {offline_time_validator.min_security_score}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 调整失败: {e}")
        return False

def test_license_loading():
    """测试许可证加载"""
    print("\n🔍 测试许可证加载...")
    
    try:
        from license.license_manager import LicenseManager
        
        # 创建许可证管理器（非严格模式）
        manager = LicenseManager(offline_mode=True, strict_mode=False)
        
        # 尝试加载许可证
        status, license_info = manager.load_license()
        
        print(f"   许可证状态: {status.value}")
        
        if license_info:
            print(f"   组织: {license_info.organization}")
            print(f"   用户: {license_info.user_id}")
            print(f"   生效时间: {license_info.start_date}")
            print(f"   到期时间: {license_info.end_date}")
            
            # 获取状态消息
            status_msg = manager.get_status_message()
            print(f"   状态消息: {status_msg}")
            
            return True
        else:
            print("   ❌ 许可证信息为空")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def create_emergency_license():
    """创建应急许可证（使用宽松的时间设置）"""
    print("\n🚑 创建应急许可证...")
    
    try:
        import uuid
        from datetime import datetime, timedelta
        from license.crypto_utils import crypto_manager
        
        # 创建一个立即生效的许可证
        now = datetime.now()
        
        license_data = {
            'organization': 'Emergency License',
            'user_id': 'emergency@local',
            'modules': ['data_cleaning', 'translation', 'summarization'],
            'issue_date': (now - timedelta(hours=1)).isoformat(),  # 1小时前签发
            'start_date': (now - timedelta(hours=1)).isoformat(),  # 1小时前生效
            'end_date': (now + timedelta(days=30)).isoformat(),    # 30天后过期
            'max_executions': -1,
            'current_executions': 0,
            'license_id': str(uuid.uuid4()),
            'offline_mode': True,
            'security_level': 'emergency',
            'last_validation': '',
            'time_proof_chain': []
        }
        
        # 生成签名
        signature = crypto_manager.generate_signature(license_data)
        license_data['signature'] = signature
        
        # 加密并保存
        encrypted_data = crypto_manager.encrypt_data(license_data)
        
        with open('config/app.license', 'w', encoding='utf-8') as f:
            f.write(encrypted_data)
        
        print("   ✅ 应急许可证创建成功")
        print(f"   - 生效时间: {license_data['start_date']}")
        print(f"   - 到期时间: {license_data['end_date']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 应急许可证创建失败: {e}")
        return False

def main():
    print("🔧 时间验证问题修复工具")
    print("=" * 50)
    
    # 步骤1：清理时间文件
    clean_time_files()
    
    # 步骤2：调整时间验证设置
    if disable_strict_time_validation():
        print("✅ 时间验证设置调整成功")
    else:
        print("❌ 时间验证设置调整失败")
    
    # 步骤3：测试许可证加载
    if test_license_loading():
        print("✅ 许可证加载测试成功")
    else:
        print("❌ 许可证加载测试失败，创建应急许可证...")
        
        # 步骤4：创建应急许可证
        if create_emergency_license():
            print("✅ 应急许可证创建成功")
            
            # 再次测试
            if test_license_loading():
                print("✅ 应急许可证加载成功")
            else:
                print("❌ 应急许可证加载仍然失败")
        else:
            print("❌ 应急许可证创建失败")
    
    print("\n" + "=" * 50)
    print("💡 修复完成，请运行以下命令测试:")
    print("   python main.py license-info")
    print("   python main.py --help")

if __name__ == '__main__':
    main()
