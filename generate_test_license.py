#!/usr/bin/env python3
"""
生成测试许可证
解决当前的许可证问题
"""

import uuid
from datetime import datetime, timedelta
from license.crypto_utils import crypto_manager
from license.license_manager import LicenseInfo

def generate_test_license():
    """生成测试许可证"""
    print("🔧 生成测试许可证...")
    
    # 生成许可证信息
    now = datetime.now()
    license_info = LicenseInfo(
        organization="Test Organization",
        user_id="<EMAIL>",
        modules=["data_cleaning", "translation", "summarization"],
        issue_date=now.isoformat(),
        start_date=now.isoformat(),  # 立即生效
        end_date=(now + timedelta(days=365)).isoformat(),  # 1年有效期
        max_executions=-1,  # 无限制
        current_executions=0,
        license_id=str(uuid.uuid4()),
        signature="",  # 临时占位符
        offline_mode=True,
        security_level="standard",
        last_validation="",
        time_proof_chain=[]
    )
    
    # 准备签名数据
    license_dict = {
        'organization': license_info.organization,
        'user_id': license_info.user_id,
        'modules': license_info.modules,
        'issue_date': license_info.issue_date,
        'start_date': license_info.start_date,
        'end_date': license_info.end_date,
        'max_executions': license_info.max_executions,
        'current_executions': license_info.current_executions,
        'license_id': license_info.license_id,
        'offline_mode': license_info.offline_mode,
        'security_level': license_info.security_level,
        'last_validation': license_info.last_validation,
        'time_proof_chain': license_info.time_proof_chain
    }
    
    try:
        # 生成签名
        signature = crypto_manager.generate_signature(license_dict)
        license_info.signature = signature
        license_dict['signature'] = signature
        
        # 加密许可证
        encrypted_license = crypto_manager.encrypt_data(license_dict)
        
        # 保存到文件
        output_file = "config/app.license"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(encrypted_license)
        
        print(f"✅ 许可证生成成功: {output_file}")
        print(f"   组织: {license_info.organization}")
        print(f"   用户: {license_info.user_id}")
        print(f"   生效时间: {license_info.start_date}")
        print(f"   到期时间: {license_info.end_date}")
        print(f"   授权模块: {', '.join(license_info.modules)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 许可证生成失败: {e}")
        return False

def main():
    print("🚀 测试许可证生成器")
    print("=" * 40)
    
    success = generate_test_license()
    
    if success:
        print("\n💡 建议:")
        print("1. 运行 'python main.py license-info' 检查许可证状态")
        print("2. 如果仍有问题，运行 'python check_license.py' 进行诊断")
    else:
        print("\n❌ 许可证生成失败，请检查错误信息")

if __name__ == '__main__':
    main()
