#!/usr/bin/env python3
"""
测试应急许可证系统
"""

def test_emergency_license():
    """测试应急许可证系统"""
    print("🧪 测试应急许可证系统")
    print("=" * 40)
    
    try:
        from emergency_license_manager import emergency_license_manager
        
        print("1. 加载许可证...")
        status, license_info = emergency_license_manager.load_license()
        
        print(f"   状态: {status.value}")
        
        if license_info:
            print(f"   组织: {license_info.organization}")
            print(f"   用户: {license_info.user_id}")
            print(f"   授权模块: {', '.join(license_info.modules)}")
            print(f"   许可证ID: {license_info.license_id}")
        
        print("\n2. 检查模块授权...")
        modules_to_test = ['data_cleaning', 'translation', 'summarization', 'invalid_module']
        
        for module in modules_to_test:
            is_authorized = emergency_license_manager.is_module_authorized(module)
            status_icon = "✅" if is_authorized else "❌"
            print(f"   {status_icon} {module}: {'授权' if is_authorized else '未授权'}")
        
        print("\n3. 获取状态消息...")
        status_msg = emergency_license_manager.get_status_message()
        print(f"   {status_msg}")
        
        print("\n4. 测试执行计数...")
        can_execute = emergency_license_manager.increment_execution_count()
        print(f"   执行权限: {'✅ 允许' if can_execute else '❌ 拒绝'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_emergency_auth():
    """测试应急认证装饰器"""
    print("\n🔐 测试应急认证功能")
    print("=" * 40)
    
    try:
        # 模拟认证装饰器功能
        from emergency_license_manager import emergency_license_manager
        
        def require_license_emergency(module_id: str):
            """应急许可证装饰器"""
            def decorator(func):
                def wrapper(*args, **kwargs):
                    status, license_info = emergency_license_manager.load_license()
                    
                    if status.value != 'valid':
                        raise PermissionError(f"许可证无效: {status.value}")
                    
                    if not emergency_license_manager.is_module_authorized(module_id):
                        authorized_modules = emergency_license_manager.get_authorized_modules()
                        raise PermissionError(
                            f"模块 '{module_id}' 未被授权。\n"
                            f"当前授权模块: {', '.join(authorized_modules)}"
                        )
                    
                    if not emergency_license_manager.increment_execution_count():
                        raise PermissionError("已达到最大执行次数限制")
                    
                    return func(*args, **kwargs)
                return wrapper
            return decorator
        
        # 测试授权的模块
        @require_license_emergency('data_cleaning')
        def test_authorized_function():
            return "数据清理功能执行成功"
        
        # 测试未授权的模块
        @require_license_emergency('invalid_module')
        def test_unauthorized_function():
            return "无效模块功能"
        
        print("1. 测试授权模块...")
        try:
            result = test_authorized_function()
            print(f"   ✅ {result}")
        except Exception as e:
            print(f"   ❌ {e}")
        
        print("\n2. 测试未授权模块...")
        try:
            result = test_unauthorized_function()
            print(f"   ❌ 意外成功: {result}")
        except PermissionError as e:
            print(f"   ✅ 正确拒绝: {e}")
        except Exception as e:
            print(f"   ❌ 意外错误: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 认证测试失败: {e}")
        return False

def main():
    print("🚨 应急许可证系统测试")
    print("=" * 50)
    
    success1 = test_emergency_license()
    success2 = test_emergency_auth()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("🎉 所有测试通过！应急系统工作正常")
        print("\n💡 现在可以使用以下方式集成应急系统:")
        print("   1. 在需要许可证验证的地方导入:")
        print("      from emergency_license_manager import emergency_license_manager")
        print("   2. 替换原有的许可证管理器调用")
        print("   3. 使用应急认证装饰器保护功能")
    else:
        print("❌ 部分测试失败，请检查应急系统")

if __name__ == '__main__':
    main()
