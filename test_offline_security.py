#!/usr/bin/env python3
"""
完全离线环境安全测试脚本
测试在无网络连接情况下的许可证验证系统
"""

import sys
import os
import logging
import json
from pathlib import Path
from datetime import datetime, timedelta

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_config_security():
    """测试配置安全性（防篡改）"""
    print("=== 测试配置安全性 ===")
    
    try:
        from core.license_config import SecureLicenseConfig
        
        config = SecureLicenseConfig()
        
        print("1. 测试硬编码安全参数...")
        min_score = config.get_min_security_score(strict_mode=True)
        max_jump = config.get_max_time_jump()
        min_sources = config.get_min_time_sources()
        
        print(f"   严格模式最低安全评分: {min_score}")
        print(f"   最大时间跳跃: {max_jump}秒")
        print(f"   最少时间源: {min_sources}")
        
        # 验证用户无法修改安全参数
        print("2. 测试安全参数保护...")
        security_params = config.get_all_security_params()
        expected_min_score = 85  # 严格模式下的硬编码值
        
        if min_score == expected_min_score:
            print("   ✓ 安全参数正确硬编码，无法被用户修改")
        else:
            print(f"   ✗ 安全参数可能被篡改: 期望{expected_min_score}, 实际{min_score}")
            return False
        
        # 测试配置完整性验证
        print("3. 测试配置完整性验证...")
        integrity_ok = config.validate_config_integrity()
        print(f"   配置完整性: {'✓ 通过' if integrity_ok else '✗ 失败'}")
        
        print("✓ 配置安全性测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 配置安全性测试失败: {e}")
        return False

def test_offline_time_validation():
    """测试完全离线时间验证"""
    print("\n=== 测试完全离线时间验证 ===")
    
    try:
        from core.offline_time_validator import offline_time_validator
        
        # 确保启用完全离线模式
        offline_time_validator.set_offline_only_mode(True)
        
        print("1. 测试离线模式设置...")
        is_offline = offline_time_validator.is_offline_only_mode()
        print(f"   完全离线模式: {'✓ 启用' if is_offline else '✗ 未启用'}")
        
        if not is_offline:
            print("   ✗ 离线模式未正确启用")
            return False
        
        print("2. 测试时间源获取（无网络）...")
        # 模拟网络不可用的情况
        start_date = (datetime.now() - timedelta(days=1)).isoformat()
        end_date = (datetime.now() + timedelta(days=30)).isoformat()
        
        result = offline_time_validator.validate_license_time(start_date, end_date)
        
        print(f"   时间验证结果: {'✓ 有效' if result.is_valid else '✗ 无效'}")
        print(f"   安全评分: {result.security_score}/100")
        print(f"   置信度: {result.confidence_level}")
        
        # 检查是否尝试了网络连接
        evidence = result.evidence
        time_sources = evidence.get('time_sources', {})
        
        if 'ntp' in time_sources:
            print("   ⚠ 警告: 在离线模式下仍尝试获取NTP时间")
        else:
            print("   ✓ 正确跳过网络时间检查")
        
        print(f"   可用时间源: {list(time_sources.keys())}")
        print(f"   时间源数量: {len(time_sources)}")
        
        # 验证离线环境下的最低要求
        if len(time_sources) >= 1 and result.security_score >= 30:
            print("   ✓ 离线环境下满足最低安全要求")
        else:
            print("   ✗ 离线环境下未满足最低安全要求")
            return False
        
        print("✓ 完全离线时间验证测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 完全离线时间验证测试失败: {e}")
        return False

def test_config_tampering_resistance():
    """测试配置文件篡改抵抗性"""
    print("\n=== 测试配置篡改抵抗性 ===")
    
    try:
        # 创建一个恶意配置文件
        malicious_config = {
            "security_settings": {
                "min_security_score": 1,  # 尝试降低安全要求
                "max_time_jump_seconds": 86400,  # 尝试允许大时间跳跃
                "require_high_confidence": False
            },
            "ui": {
                "language": "zh"  # 合法的用户配置
            }
        }
        
        config_file = Path("config/test_malicious_config.json")
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(malicious_config, f, indent=2)
        
        print("1. 创建恶意配置文件...")
        print("   ✓ 恶意配置文件已创建")
        
        # 尝试加载恶意配置
        print("2. 测试恶意配置加载...")
        from core.license_config import SecureLicenseConfig
        
        config = SecureLicenseConfig(str(config_file))
        
        # 检查安全参数是否被篡改
        actual_min_score = config.get_min_security_score(strict_mode=True)
        actual_max_jump = config.get_max_time_jump()
        
        print(f"   实际最低安全评分: {actual_min_score}")
        print(f"   实际最大时间跳跃: {actual_max_jump}")
        
        # 验证安全参数未被篡改
        if actual_min_score == 85 and actual_max_jump == 180:
            print("   ✓ 安全参数未被恶意配置篡改")
        else:
            print("   ✗ 安全参数被恶意配置篡改")
            return False
        
        # 验证合法用户配置仍然有效
        language = config.get_user_param("ui.language", "en")
        if language == "zh":
            print("   ✓ 合法用户配置正常加载")
        else:
            print("   ✗ 合法用户配置加载失败")
        
        # 清理测试文件
        config_file.unlink()
        print("3. 清理测试文件...")
        print("   ✓ 测试文件已清理")
        
        print("✓ 配置篡改抵抗性测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 配置篡改抵抗性测试失败: {e}")
        # 确保清理测试文件
        try:
            config_file.unlink()
        except:
            pass
        return False

def test_license_manager_security():
    """测试许可证管理器安全性"""
    print("\n=== 测试许可证管理器安全性 ===")
    
    try:
        from license.license_manager import LicenseManager
        
        print("1. 创建安全许可证管理器...")
        manager = LicenseManager(offline_mode=True, strict_mode=True)
        
        print(f"   离线模式: {manager.offline_mode}")
        print(f"   严格模式: {manager.strict_mode}")
        print(f"   最低安全评分: {manager.min_security_score}")
        print(f"   要求高置信度: {manager.require_high_confidence}")
        
        # 验证安全参数符合预期
        if manager.min_security_score >= 75:
            print("   ✓ 安全评分要求符合预期")
        else:
            print(f"   ✗ 安全评分要求过低: {manager.min_security_score}")
            return False
        
        print("2. 测试许可证加载...")
        status, license_info = manager.load_license()
        print(f"   许可证状态: {status.value}")
        
        # 获取安全报告
        if hasattr(manager, 'get_security_report'):
            print("3. 测试安全报告生成...")
            security_report = manager.get_security_report()
            print(f"   安全报告状态: {security_report.get('status', 'unknown')}")
            
            if 'security_score' in security_report:
                score = security_report['security_score']
                print(f"   当前安全评分: {score}/100")
                
                if score >= manager.min_security_score:
                    print("   ✓ 安全评分满足要求")
                else:
                    print(f"   ⚠ 安全评分低于要求: {score} < {manager.min_security_score}")
        
        print("✓ 许可证管理器安全性测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 许可证管理器安全性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始完全离线环境安全测试...\n")
    
    tests = [
        test_config_security,
        test_offline_time_validation,
        test_config_tampering_resistance,
        test_license_manager_security
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"测试执行异常: {e}")
    
    print(f"\n=== 安全测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🔒 所有安全测试通过！系统可以安全地在完全离线环境中运行。")
        return 0
    else:
        print("⚠️ 部分安全测试失败，请检查系统配置。")
        return 1

if __name__ == '__main__':
    sys.exit(main())
