
# 应急许可证管理器
import os
from datetime import datetime
from typing import Tuple, Optional, List
from dataclasses import dataclass
from enum import Enum
from emergency_crypto import emergency_crypto_manager

class LicenseStatus(Enum):
    VALID = "valid"
    EXPIRED = "expired"
    INVALID = "invalid"
    NOT_FOUND = "not_found"
    CORRUPTED = "corrupted"

@dataclass
class LicenseInfo:
    organization: str
    user_id: str
    modules: List[str]
    issue_date: str
    start_date: str
    end_date: str
    max_executions: int
    current_executions: int
    license_id: str
    signature: str

class EmergencyLicenseManager:
    def __init__(self):
        self.license_file = "config/app.license"
        self._license_info = None
        self._license_status = LicenseStatus.NOT_FOUND
    
    def load_license(self) -> Tuple[LicenseStatus, Optional[LicenseInfo]]:
        if not os.path.exists(self.license_file):
            return LicenseStatus.NOT_FOUND, None
        
        try:
            with open(self.license_file, 'r', encoding='utf-8') as f:
                encrypted_data = f.read().strip()
            
            license_data = emergency_crypto_manager.decrypt_data(encrypted_data)
            
            # 简单验证签名
            signature = license_data.get('signature', '')
            if not emergency_crypto_manager.verify_signature(license_data, signature):
                return LicenseStatus.CORRUPTED, None
            
            self._license_info = LicenseInfo(**license_data)
            
            # 简化的时间验证（只检查基本有效期）
            now = datetime.now()
            try:
                end_date = datetime.fromisoformat(self._license_info.end_date)
                if now > end_date:
                    return LicenseStatus.EXPIRED, self._license_info
            except:
                pass  # 忽略时间解析错误
            
            self._license_status = LicenseStatus.VALID
            return LicenseStatus.VALID, self._license_info
            
        except Exception as e:
            print(f"许可证加载失败: {e}")
            return LicenseStatus.CORRUPTED, None
    
    def get_authorized_modules(self) -> List[str]:
        if self._license_status == LicenseStatus.VALID and self._license_info:
            return self._license_info.modules.copy()
        return []
    
    def is_module_authorized(self, module_name: str) -> bool:
        return module_name in self.get_authorized_modules()
    
    def get_license_info(self) -> Optional[LicenseInfo]:
        return self._license_info
    
    def get_status_message(self) -> str:
        if self._license_status == LicenseStatus.VALID and self._license_info:
            return f"应急许可证有效 - 授权给: {self._license_info.organization}\n授权模块: {', '.join(self._license_info.modules)}"
        elif self._license_status == LicenseStatus.EXPIRED:
            return "许可证已过期"
        elif self._license_status == LicenseStatus.NOT_FOUND:
            return "未找到许可证文件"
        else:
            return "许可证无效"
    
    def increment_execution_count(self) -> bool:
        return True  # 应急模式下总是允许执行

# 创建全局实例
emergency_license_manager = EmergencyLicenseManager()
