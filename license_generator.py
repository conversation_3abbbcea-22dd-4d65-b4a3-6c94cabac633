#!/usr/bin/env python3
"""
许可证生成工具
仅供管理员使用，用于生成加密的许可证文件
"""

import argparse
import uuid
from datetime import datetime, timedelta
from license.crypto_utils import crypto_manager
from license.license_manager import LicenseInfo


class LicenseGenerator:
    def __init__(self):
        self.available_modules = [
            'data_cleaning',
            'translation',
            'summarization'
        ]

    def generate_license(self,
                         organization: str,
                         user_id: str,
                         modules: list,
                         duration_days: int,
                         max_executions: int = -1,
                         output_file: str = None) -> str:
        """生成许可证"""

        # 验证模块
        invalid_modules = [m for m in modules if m not in self.available_modules]
        if invalid_modules:
            raise ValueError(f"无效的模块: {invalid_modules}")

        # 生成许可证信息
        now = datetime.now()
        license_info = LicenseInfo(
            organization=organization,
            user_id=user_id,
            modules=modules,
            issue_date=now.isoformat(),
            start_date=now.isoformat(),
            end_date=(now + timedelta(days=duration_days)).isoformat(),
            max_executions=max_executions,
            current_executions=0,
            license_id=str(uuid.uuid4()),
            signature=""  # 临时占位符
        )

        # 生成签名
        license_dict = {
            'organization': license_info.organization,
            'user_id': license_info.user_id,
            'modules': license_info.modules,
            'issue_date': license_info.issue_date,
            'start_date': license_info.start_date,
            'end_date': license_info.end_date,
            'max_executions': license_info.max_executions,
            'current_executions': license_info.current_executions,
            'license_id': license_info.license_id
        }

        signature = crypto_manager.generate_signature(license_dict)
        license_info.signature = signature
        license_dict['signature'] = signature

        # 加密许可证
        encrypted_license = crypto_manager.encrypt_data(license_dict)

        # 保存到文件
        if not output_file:
            output_file = f"license_{organization}_{user_id}.license"

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(encrypted_license)

        return output_file

    def display_license_info(self, license_file: str):
        """显示许可证信息（用于验证）"""
        try:
            with open(license_file, 'r', encoding='utf-8') as f:
                encrypted_data = f.read().strip()

            license_data = crypto_manager.decrypt_data(encrypted_data)

            print("=== 许可证信息 ===")
            print(f"组织: {license_data['organization']}")
            print(f"用户ID: {license_data['user_id']}")
            print(f"授权模块: {', '.join(license_data['modules'])}")
            print(f"签发日期: {license_data['issue_date']}")
            print(f"生效日期: {license_data['start_date']}")
            print(f"到期日期: {license_data['end_date']}")
            print(
                f"最大执行次数: {'无限制' if license_data['max_executions'] == -1 else license_data['max_executions']}")
            print(f"许可证ID: {license_data['license_id']}")
            print(f"签名: {license_data['signature'][:20]}...")

        except Exception as e:
            print(f"读取许可证失败: {e}")


def main():
    parser = argparse.ArgumentParser(description='许可证生成工具')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')

    # 生成许可证命令
    gen_parser = subparsers.add_parser('generate', help='生成新许可证')
    gen_parser.add_argument('--org', required=True, help='组织名称')
    gen_parser.add_argument('--user', required=True, help='用户ID')
    gen_parser.add_argument('--modules', nargs='+',
                            choices=['data_cleaning', 'translation', 'summarization'],
                            required=True, help='授权模块')
    gen_parser.add_argument('--days', type=int, default=365, help='有效期天数')
    gen_parser.add_argument('--max-exec', type=int, default=-1, help='最大执行次数 (-1表示无限制)')
    gen_parser.add_argument('--output', help='输出文件路径')

    # 查看许可证命令
    view_parser = subparsers.add_parser('view', help='查看许可证信息')
    view_parser.add_argument('license_file', help='许可证文件路径')

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    generator = LicenseGenerator()

    if args.command == 'generate':
        try:
            output_file = generator.generate_license(
                organization=args.org,
                user_id=args.user,
                modules=args.modules,
                duration_days=args.days,
                max_executions=args.max_exec,
                output_file=args.output
            )
            print(f"许可证生成成功: {output_file}")

            # 显示生成的许可证信息
            generator.display_license_info(output_file)

        except Exception as e:
            print(f"生成许可证失败: {e}")

    elif args.command == 'view':
        generator.display_license_info(args.license_file)


if __name__ == '__main__':
    main()