#!/usr/bin/env python3
"""
许可证检查工具
用于诊断许可证和时间问题
"""

import json
from datetime import datetime
from pathlib import Path

def check_current_time():
    """检查当前系统时间"""
    now = datetime.now()
    print(f"当前系统时间: {now.isoformat()}")
    return now

def check_time_anchors():
    """检查时间锚点文件"""
    anchor_file = Path("config/.time_anchors")
    if anchor_file.exists():
        try:
            with open(anchor_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            
            # 分离JSON和哈希
            lines = content.split('\n')
            json_content = '\n'.join(lines[:-1])
            hash_value = lines[-1]
            
            anchor_data = json.loads(json_content)
            creation_time = datetime.fromisoformat(anchor_data['creation_time'])
            
            print(f"时间锚点创建时间: {creation_time.isoformat()}")
            
            now = datetime.now()
            time_diff = (now - creation_time).total_seconds()
            print(f"时间差异: {time_diff:.2f}秒 ({time_diff/3600:.2f}小时)")
            
            if abs(time_diff) > 300:  # 5分钟
                print("⚠️ 时间差异过大，可能导致验证失败")
            else:
                print("✅ 时间差异在正常范围内")
                
        except Exception as e:
            print(f"❌ 时间锚点文件读取失败: {e}")
    else:
        print("❌ 时间锚点文件不存在")

def check_license_file():
    """检查许可证文件"""
    license_file = Path("config/app.license")
    if not license_file.exists():
        print("❌ 许可证文件不存在")
        return
    
    print("📄 许可证文件存在")
    
    # 尝试解密许可证
    try:
        from license.crypto_utils import legacy_crypto_manager
        
        with open(license_file, 'r', encoding='utf-8') as f:
            encrypted_data = f.read().strip()
        
        license_data = legacy_crypto_manager.decrypt_data(encrypted_data)
        
        print("✅ 许可证解密成功（使用旧系统）")
        print(f"   组织: {license_data.get('organization', 'N/A')}")
        print(f"   用户: {license_data.get('user_id', 'N/A')}")
        print(f"   签发时间: {license_data.get('issue_date', 'N/A')}")
        print(f"   生效时间: {license_data.get('start_date', 'N/A')}")
        print(f"   到期时间: {license_data.get('end_date', 'N/A')}")
        
        # 检查时间有效性
        now = datetime.now()
        try:
            start_date = datetime.fromisoformat(license_data['start_date'])
            end_date = datetime.fromisoformat(license_data['end_date'])
            
            if now < start_date:
                print(f"⚠️ 许可证尚未生效，还需等待 {(start_date - now).total_seconds():.0f} 秒")
            elif now > end_date:
                print(f"❌ 许可证已过期，过期时间: {(now - end_date).total_seconds():.0f} 秒")
            else:
                print("✅ 许可证在有效期内")
                
        except Exception as e:
            print(f"❌ 时间解析失败: {e}")
            
    except Exception as e:
        print(f"❌ 许可证解密失败: {e}")

def main():
    print("🔍 许可证系统诊断")
    print("=" * 50)
    
    print("\n1. 检查系统时间")
    check_current_time()
    
    print("\n2. 检查时间锚点")
    check_time_anchors()
    
    print("\n3. 检查许可证文件")
    check_license_file()
    
    print("\n" + "=" * 50)
    print("💡 解决建议:")
    print("1. 如果时间差异过大，删除 config/.time_anchors 文件")
    print("2. 如果许可证未生效，等待或重新生成许可证")
    print("3. 如果许可证已过期，重新生成新的许可证")

if __name__ == '__main__':
    main()
