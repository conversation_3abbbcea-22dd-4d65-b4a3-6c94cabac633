{
  "policy_version": "1.0",
  "enforcement_level": "strict",
  "allowed_user_modifications": [
    "logging.log_level",
    "logging.enabled",
    "logging.events_to_log.*",
    "alerts.enabled",
    "alerts.alert_conditions.*",
    "maintenance.auto_cleanup.cleanup_interval_hours",
    "maintenance.auto_cleanup.keep_logs_days",
    "maintenance.auto_cleanup.keep_proofs_days",
    "user_interface.*",
    "performance.cache_system_fingerprint",
    "performance.fingerprint_cache_duration_minutes",
    "performance.enable_fast_mode",
    "compatibility.fallback_mode.enabled",
    "compatibility.legacy_support.*"
  ],
  "protected_parameters": [
    "core_security_components.*",
    "security_enforcement.*",
    "time_validation.min_security_score",
    "security_levels.*.min_score"
  ],
  "parameter_constraints": {
    "time_validation.max_time_jump_seconds": {
      "min": 300,
      "max": 7200
    },
    "anomaly_detection.time_source_max_diff_seconds": {
      "min": 60,
      "max": 1800
    }
  }
}
# INTEGRITY_HASH: 9ba90b777ab15d669899332b70990ecb053719e88e501e417c506b2fd6f7d32e
# SIGNATURE: 23b588b98db533c3d1b0fd10898d5296c83efda0fd085013f70c5299d107d481