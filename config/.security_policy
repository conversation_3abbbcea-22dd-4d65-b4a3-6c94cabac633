{
  "policy_version": "1.0",
  "enforcement_level": "strict",
  "allowed_user_modifications": [
    "logging.log_level",
    "logging.enabled",
    "alerts.enabled",
    "maintenance.auto_cleanup.cleanup_interval_hours"
  ],
  "protected_parameters": [
    "core_security_components.*",
    "security_enforcement.*",
    "time_validation.min_security_score",
    "security_levels.*.min_score"
  ],
  "parameter_constraints": {
    "time_validation.max_time_jump_seconds": {
      "min": 300,
      "max": 7200
    },
    "anomaly_detection.time_source_max_diff_seconds": {
      "min": 60,
      "max": 1800
    }
  }
}
# INTEGRITY_HASH: 43c24e83c8608418c08e63d009f5bc7fdc0cf8a8d435a65d35cd7d547782e32f
# SIGNATURE: a2060f4bc0372f9cf8ecf453872521c35dc322637903ce9129c33a908973a1a0