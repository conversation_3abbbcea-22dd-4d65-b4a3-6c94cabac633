# 安全离线许可证系统部署指南

## 快速开始

### 1. 安装依赖

```bash
# 安装基础依赖
pip install cryptography psutil

# 可选：安装Argon2以获得更强的密钥派生
pip install argon2-cffi
```

### 2. 配置系统

编辑 `config/license_config.json`：

```json
{
  "license_settings": {
    "offline_mode": true,
    "strict_mode": true
  },
  "security_settings": {
    "min_security_score": 70,
    "require_high_confidence": true,
    "max_time_jump_seconds": 300,
    "min_time_sources": 2,
    "max_source_deviation_seconds": 600
  }
}
```

### 3. 生成新许可证

使用更新后的许可证生成器：

```bash
python license_generator.py generate \
  --org "Your Organization" \
  --user "<EMAIL>" \
  --modules data_cleaning translation summarization \
  --days 365 \
  --output config/app.license
```

### 4. 测试系统

```bash
# 运行完整测试
python test_offline_license.py

# 测试CLI功能
python main.py license-info
python main.py security-check
```

## 安全配置建议

### 生产环境配置

```json
{
  "license_settings": {
    "offline_mode": true,
    "strict_mode": true
  },
  "security_settings": {
    "min_security_score": 80,
    "require_high_confidence": true,
    "max_time_jump_seconds": 180,
    "min_time_sources": 3,
    "max_source_deviation_seconds": 300
  },
  "time_validation": {
    "enable_ntp_check": true,
    "ntp_timeout_seconds": 2
  }
}
```

### 开发环境配置

```json
{
  "license_settings": {
    "offline_mode": true,
    "strict_mode": false
  },
  "security_settings": {
    "min_security_score": 50,
    "require_high_confidence": false,
    "max_time_jump_seconds": 600
  }
}
```

## 监控和维护

### 1. 日志监控

设置日志级别：

```python
import logging
logging.basicConfig(level=logging.INFO)
```

关键日志事件：
- 许可证验证失败
- 安全评分过低
- 时间异常检测
- 密钥系统错误

### 2. 安全检查脚本

创建定期安全检查：

```python
#!/usr/bin/env python3
from license.license_manager import LicenseManager

def security_check():
    manager = LicenseManager()
    status, _ = manager.load_license()
    
    if status.value in ['security_risk', 'time_tampered']:
        # 发送警报
        print(f"ALERT: License security issue - {status.value}")
        
    report = manager.get_security_report()
    if report.get('security_score', 0) < 60:
        print(f"WARNING: Low security score - {report['security_score']}")

if __name__ == '__main__':
    security_check()
```

### 3. 密钥轮换

```python
from license.crypto_utils import crypto_manager

# 执行密钥轮换
if crypto_manager.rotate_keys():
    print("密钥轮换成功")
else:
    print("密钥轮换失败")
```

## 故障排除

### 常见问题

1. **许可证解密失败**
   - 检查硬件环境是否发生重大变化
   - 验证许可证文件完整性
   - 重新生成许可证

2. **安全评分过低**
   - 检查系统时间设置
   - 验证时间源可用性
   - 调整安全参数

3. **时间验证失败**
   - 确保系统时间正确
   - 检查NTP服务可用性
   - 验证时间锚点文件

### 调试命令

```bash
# 查看详细许可证信息
python main.py license-info

# 执行安全检查
python main.py security-check

# 运行测试套件
python test_offline_license.py
```

## 安全最佳实践

1. **定期更新**
   - 每90天轮换密钥
   - 定期更新安全配置
   - 监控安全评分趋势

2. **环境保护**
   - 保护配置文件访问权限
   - 监控系统时间变化
   - 记录所有许可证操作

3. **备份策略**
   - 备份密钥信息文件
   - 保存许可证配置
   - 记录硬件指纹信息

## 性能优化

1. **缓存配置**
   ```json
   {
     "performance": {
       "cache_system_fingerprint": true,
       "fingerprint_cache_duration_minutes": 60
     }
   }
   ```

2. **快速模式**
   ```json
   {
     "performance": {
       "enable_fast_mode": true,
       "max_proof_chain_length": 25
     }
   }
   ```

## 集成示例

### 在现有应用中集成

```python
from core.auth import require_license

@require_license('your_module')
def your_function():
    # 您的业务逻辑
    pass
```

### 自定义验证逻辑

```python
from license.license_manager import LicenseManager, LicenseStatus

def custom_license_check():
    manager = LicenseManager(strict_mode=True)
    status, info = manager.load_license()
    
    if status != LicenseStatus.VALID:
        raise Exception(f"License invalid: {manager.get_status_message()}")
    
    # 自定义检查
    security_report = manager.get_security_report()
    if security_report.get('security_score', 0) < 80:
        print("Warning: Security score below recommended threshold")
    
    return info
```

## 支持和维护

如需技术支持或报告问题，请提供：
1. 系统配置信息
2. 错误日志
3. 安全报告输出
4. 硬件环境描述

系统已经过全面测试，可以安全部署到生产环境中。
