import os
import json
import time
import logging
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Tuple, Dict, Any
from dataclasses import dataclass, asdict
from enum import Enum

from license.crypto_utils import crypto_manager
from core.offline_time_validator import offline_time_validator, OfflineTimeValidationResult
from core.license_config import license_config

logger = logging.getLogger(__name__)

class LicenseStatus(Enum):
    VALID = "valid"
    EXPIRED = "expired"
    INVALID = "invalid"
    NOT_FOUND = "not_found"
    CORRUPTED = "corrupted"
    TIME_TAMPERED = "time_tampered"
    SECURITY_RISK = "security_risk"

@dataclass
class LicenseInfo:
    """许可证信息结构（增强版）"""
    organization: str           # 组织名称
    user_id: str               # 用户标识
    modules: List[str]         # 授权模块列表
    issue_date: str            # 签发日期
    start_date: str            # 生效日期
    end_date: str              # 到期日期
    max_executions: int        # 最大执行次数 (-1表示无限制)
    current_executions: int    # 当前执行次数
    license_id: str            # 许可证唯一标识
    signature: str             # 数字签名
    # 离线模式增强字段
    offline_mode: bool = True
    security_level: str = "standard"
    last_validation: str = ""
    time_proof_chain: List[str] = None

    def __post_init__(self):
        if self.time_proof_chain is None:
            self.time_proof_chain = []

class LicenseManager:
    def __init__(self, license_file: str = None, offline_mode: bool = None, strict_mode: bool = None):
        """初始化许可证管理器（支持离线模式）"""
        self.license_file = license_file or self._get_default_license_path()
        self._license_info: Optional[LicenseInfo] = None
        self._license_status: LicenseStatus = LicenseStatus.NOT_FOUND
        self._time_validation_result: Optional[OfflineTimeValidationResult] = None

        # 从配置文件加载设置（如果未明确指定）
        self.offline_mode = offline_mode if offline_mode is not None else license_config.is_offline_mode()
        self.strict_mode = strict_mode if strict_mode is not None else license_config.is_strict_mode()
        self.min_security_score = license_config.get_min_security_score()
        self.require_high_confidence = license_config.get('security_settings.require_high_confidence', True)

        logger.info(f"许可证管理器初始化 - 离线模式: {self.offline_mode}, 严格模式: {self.strict_mode}")
    
    def _get_default_license_path(self) -> str:
        """获取默认许可证文件路径"""
        config_dir = Path(__file__).parent.parent / "config"
        return str(config_dir / "app.license")
    
    def load_license(self) -> Tuple[LicenseStatus, Optional[LicenseInfo]]:
        """加载并验证许可证"""
        if not os.path.exists(self.license_file):
            self._license_status = LicenseStatus.NOT_FOUND
            return self._license_status, None
        
        try:
            # 读取加密的许可证文件
            with open(self.license_file, 'r', encoding='utf-8') as f:
                encrypted_data = f.read().strip()
            
            # 解密许可证数据
            license_data = crypto_manager.decrypt_data(encrypted_data)
            
            # 验证签名
            signature = license_data.pop('signature', '')
            if not crypto_manager.verify_signature(license_data, signature):
                self._license_status = LicenseStatus.CORRUPTED
                return self._license_status, None
            
            # 重新添加签名到数据中
            license_data['signature'] = signature
            
            # 创建许可证信息对象
            self._license_info = LicenseInfo(**license_data)
            
            # 验证许可证有效性
            self._license_status = self._validate_license(self._license_info)
            
            return self._license_status, self._license_info
            
        except Exception as e:
            print(f"许可证加载失败: {e}")
            self._license_status = LicenseStatus.CORRUPTED
            return self._license_status, None
    
    def _validate_license(self, license_info: LicenseInfo) -> LicenseStatus:
        """验证许可证有效性（支持离线模式）"""
        if self.offline_mode:
            return self._validate_license_offline(license_info)
        else:
            return self._validate_license_online(license_info)

    def _validate_license_online(self, license_info: LicenseInfo) -> LicenseStatus:
        """在线模式许可证验证"""
        now = datetime.now()

        # 检查日期格式和有效性
        try:
            start_date = datetime.fromisoformat(license_info.start_date)
            end_date = datetime.fromisoformat(license_info.end_date)
        except ValueError:
            logger.error("许可证日期格式无效")
            return LicenseStatus.INVALID

        # 检查是否在有效期内
        if now < start_date or now > end_date:
            return LicenseStatus.EXPIRED

        # 检查执行次数限制
        if 0 < license_info.max_executions <= license_info.current_executions:
            return LicenseStatus.EXPIRED

        return LicenseStatus.VALID

    def _validate_license_offline(self, license_info: LicenseInfo) -> LicenseStatus:
        """离线模式许可证验证"""
        try:
            # 1. 执行离线时间验证
            time_result = offline_time_validator._validate_license_time_and_update(
                license_info.start_date,
                license_info.end_date
            )
            self._time_validation_result = time_result

            # 2. 检查时间验证结果
            if not time_result.is_valid:
                logger.warning(f"时间验证失败: {time_result.warnings}")
                return LicenseStatus.EXPIRED

            # 3. 安全评分检查
            if time_result.security_score < self.min_security_score:
                logger.warning(f"安全评分不足: {time_result.security_score}/{100}")
                if self.strict_mode:
                    return LicenseStatus.SECURITY_RISK

            # 4. 置信度检查
            if self.require_high_confidence and time_result.confidence_level == "low":
                logger.warning("时间验证置信度过低")
                return LicenseStatus.SECURITY_RISK

            # 5. 检查执行次数限制
            if (license_info.max_executions > 0 and
                license_info.current_executions >= license_info.max_executions):
                return LicenseStatus.EXPIRED

            # 6. 更新最后验证时间
            license_info.last_validation = time_result.current_time.isoformat()

            return LicenseStatus.VALID

        except Exception as e:
            logger.error(f"离线许可证验证失败: {e}")
            return LicenseStatus.INVALID
    
    def get_authorized_modules(self) -> List[str]:
        """获取授权的模块列表"""
        if self._license_status == LicenseStatus.VALID and self._license_info:
            return self._license_info.modules.copy()
        return []
    
    def is_module_authorized(self, module_name: str) -> bool:
        """检查模块是否被授权"""
        return module_name in self.get_authorized_modules()
    
    def get_license_info(self) -> Optional[LicenseInfo]:
        """获取许可证信息"""
        return self._license_info
    
    def get_license_status(self) -> LicenseStatus:
        """获取许可证状态"""
        return self._license_status
    
    def increment_execution_count(self) -> bool:
        """增加执行计数（支持离线模式）"""
        if (self._license_status == LicenseStatus.VALID and
            self._license_info and
            (self._license_info.max_executions == -1 or
             self._license_info.current_executions < self._license_info.max_executions)):

            # 更新执行次数
            self._license_info.current_executions += 1

            # 如果是离线模式，添加时间证明到链中
            if self.offline_mode:
                current_time = datetime.now()
                time_proof = self._create_execution_proof(current_time)
                self._license_info.time_proof_chain.append(time_proof)

                # 保持证明链长度合理
                if len(self._license_info.time_proof_chain) > 50:
                    self._license_info.time_proof_chain = self._license_info.time_proof_chain[-25:]

            self._save_updated_license()
            return True
        return False

    def _create_execution_proof(self, execution_time: datetime) -> str:
        """创建执行证明"""
        try:
            proof_data = {
                'execution_time': execution_time.isoformat(),
                'execution_count': self._license_info.current_executions + 1,
                'license_id': self._license_info.license_id,
                'system_state': self._get_system_state_summary()
            }

            # 生成证明签名
            proof_json = json.dumps(proof_data, sort_keys=True)
            proof_signature = crypto_manager.generate_signature(proof_data)

            proof_data['signature'] = proof_signature
            return json.dumps(proof_data, separators=(',', ':'))

        except Exception as e:
            logger.error(f"创建执行证明失败: {e}")
            return ""

    def _get_system_state_summary(self) -> str:
        """获取系统状态摘要"""
        try:
            import platform
            import psutil

            state_info = {
                'platform': platform.system(),
                'uptime': int(time.time() - psutil.boot_time()) if hasattr(psutil, 'boot_time') else 0,
                'cpu_count': psutil.cpu_count() if hasattr(psutil, 'cpu_count') else 0
            }

            return json.dumps(state_info, sort_keys=True)

        except Exception as e:
            logger.debug(f"获取系统状态失败: {e}")
            return "unknown"
    
    def _save_updated_license(self):
        """保存更新后的许可证"""
        if not self._license_info:
            return
        
        try:
            # 准备数据
            license_data = asdict(self._license_info)
            signature = license_data.pop('signature')
            
            # 重新生成签名
            new_signature = crypto_manager.generate_signature(license_data)
            license_data['signature'] = new_signature
            
            # 加密并保存
            encrypted_data = crypto_manager.encrypt_data(license_data)
            with open(self.license_file, 'w', encoding='utf-8') as f:
                f.write(encrypted_data)
                
            # 更新内存中的签名
            self._license_info.signature = new_signature
            
        except Exception as e:
            print(f"保存许可证失败: {e}")
    
    def get_status_message(self) -> str:
        """获取状态消息（支持离线模式）"""
        if self._license_status == LicenseStatus.VALID:
            if self._license_info:
                # 使用验证时间或当前时间
                current_time = (self._time_validation_result.current_time
                              if self._time_validation_result
                              else datetime.now())

                end_date = datetime.fromisoformat(self._license_info.end_date)
                days_left = (end_date - current_time).days
                exec_info = ""
                if self._license_info.max_executions > 0:
                    remaining = self._license_info.max_executions - self._license_info.current_executions
                    exec_info = f", 剩余执行次数: {remaining}"

                mode_info = " (离线模式)" if self.offline_mode else ""
                security_info = ""

                if self.offline_mode and self._time_validation_result:
                    security_info = f"\n安全评分: {self._time_validation_result.security_score}/100"

                return (f"许可证有效{mode_info} - 授权给: {self._license_info.organization}\n"
                       f"到期时间: {self._license_info.end_date} ({days_left}天后){exec_info}\n"
                       f"授权模块: {', '.join(self._license_info.modules)}{security_info}")
            return ''

        elif self._license_status == LicenseStatus.EXPIRED:
            return "许可证已过期，请联系管理员更新许可证"

        elif self._license_status == LicenseStatus.TIME_TAMPERED:
            return "检测到时间篡改，许可证验证失败。请确保系统时间正确并联系管理员。"

        elif self._license_status == LicenseStatus.SECURITY_RISK:
            return "检测到安全风险，许可证验证受限。请检查系统完整性并联系管理员。"

        elif self._license_status == LicenseStatus.NOT_FOUND:
            return "未找到许可证文件，请联系管理员获取许可证"

        elif self._license_status == LicenseStatus.CORRUPTED:
            return "许可证文件损坏或被篡改，请联系管理员重新获取许可证"

        else:
            return "许可证无效，请联系管理员"

    def get_security_report(self) -> Dict[str, Any]:
        """获取详细的安全报告（离线模式）"""
        if not self.offline_mode or not self._time_validation_result:
            return {"status": "unknown", "message": "未进行离线时间验证"}

        report = {
            "license_status": self._license_status.value,
            "offline_mode": True,
            "security_score": self._time_validation_result.security_score,
            "confidence_level": self._time_validation_result.confidence_level,
            "current_time": self._time_validation_result.current_time.isoformat(),
            "warnings": self._time_validation_result.warnings,
            "evidence": self._time_validation_result.evidence,
            "strict_mode": self.strict_mode,
            "min_security_score": self.min_security_score
        }

        return report

# 全局许可证管理器实例
license_manager = LicenseManager()