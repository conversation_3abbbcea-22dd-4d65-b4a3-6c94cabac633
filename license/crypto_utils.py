import base64
import hashlib
import hmac
import json
import os
import platform
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
import logging

from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

# 尝试导入Argon2，如果不可用则回退到PBKDF2
try:
    from argon2 import PasswordHasher
    from argon2.low_level import hash_secret, Type
    ARGON2_AVAILABLE = True
except ImportError:
    ARGON2_AVAILABLE = False

logger = logging.getLogger(__name__)

class SecureCryptoManager:
    """安全的加密管理器，使用基于硬件特征的动态密钥生成"""

    def __init__(self, master_key: str = None, config_dir: str = "config"):
        """初始化安全加密管理器"""
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)

        # 密钥存储文件
        self.key_info_file = self.config_dir / ".crypto_keyinfo"

        # 初始化密钥系统
        self._master_key = master_key
        self._salt = None
        self._key_version = 1
        self._fernet = None

        # 加载或生成密钥信息
        self._initialize_crypto_system()

    def _initialize_crypto_system(self):
        """初始化加密系统"""
        try:
            if self.key_info_file.exists():
                self._load_key_info()
            else:
                self._generate_new_key_info()

            logger.info(f"加密系统初始化成功，密钥版本: {self._key_version}")
        except Exception as e:
            logger.error(f"加密系统初始化失败: {e}")
            # 使用应急密钥系统
            self._use_emergency_keys()

    def _generate_hardware_fingerprint(self) -> str:
        """生成基于硬件特征的指纹"""
        try:
            # 收集系统硬件信息
            system_info = {
                'platform': platform.platform(),
                'processor': platform.processor(),
                'machine': platform.machine(),
                'node': platform.node(),
                'system': platform.system(),
                'release': platform.release(),
            }

            # 尝试获取更多硬件信息
            try:
                import psutil
                system_info.update({
                    'cpu_count': psutil.cpu_count(logical=False),
                    'cpu_freq': psutil.cpu_freq().max if psutil.cpu_freq() else 0,
                    'memory_total': psutil.virtual_memory().total,
                })
            except ImportError:
                logger.warning("psutil不可用，使用基础硬件信息")

            # 生成稳定的硬件指纹
            fingerprint_data = json.dumps(system_info, sort_keys=True)
            return hashlib.sha256(fingerprint_data.encode()).hexdigest()

        except Exception as e:
            logger.warning(f"硬件指纹生成失败，使用备用方案: {e}")
            # 备用方案：使用系统基础信息
            fallback_data = f"{platform.system()}-{platform.machine()}-{uuid.getnode()}"
            return hashlib.sha256(fallback_data.encode()).hexdigest()

    def _generate_new_key_info(self):
        """生成新的密钥信息"""
        # 生成随机盐值
        self._salt = os.urandom(32)

        # 基于硬件指纹生成主密钥
        hardware_fp = self._generate_hardware_fingerprint()

        if self._master_key:
            # 如果提供了主密钥，将其与硬件指纹结合
            combined_key = f"{self._master_key}-{hardware_fp}"
        else:
            # 使用硬件指纹作为主密钥基础
            combined_key = f"SecureLicense-{hardware_fp}-2024"

        # 保存密钥信息（加密存储）
        key_info = {
            'salt': base64.b64encode(self._salt).decode(),
            'key_version': self._key_version,
            'hardware_fp_hash': hashlib.sha256(hardware_fp.encode()).hexdigest()[:16],
            'created_at': json.dumps(datetime.now().isoformat())
        }

        self._save_key_info(key_info, combined_key)

    def _save_key_info(self, key_info: Dict[str, Any], master_key: str):
        """安全保存密钥信息"""
        try:
            # 使用主密钥加密密钥信息
            info_json = json.dumps(key_info)
            info_hash = hashlib.sha256(info_json.encode()).hexdigest()

            # 简单的XOR加密（用于存储密钥信息）
            key_bytes = hashlib.sha256(master_key.encode()).digest()
            encrypted_info = self._xor_encrypt(info_json.encode(), key_bytes)

            # 保存到文件
            with open(self.key_info_file, 'wb') as f:
                f.write(encrypted_info)
                f.write(b'\n')
                f.write(info_hash.encode())

        except Exception as e:
            logger.error(f"保存密钥信息失败: {e}")
            raise

    def _load_key_info(self):
        """加载密钥信息"""
        try:
            with open(self.key_info_file, 'rb') as f:
                content = f.read()

            # 分离加密数据和哈希
            parts = content.split(b'\n')
            if len(parts) != 2:
                raise ValueError("密钥信息文件格式错误")

            encrypted_info = parts[0]
            stored_hash = parts[1].decode()

            # 尝试解密（需要重新生成主密钥）
            hardware_fp = self._generate_hardware_fingerprint()

            # 尝试不同的主密钥组合
            possible_keys = []
            if self._master_key:
                possible_keys.append(f"{self._master_key}-{hardware_fp}")
            possible_keys.append(f"SecureLicense-{hardware_fp}-2024")

            for master_key in possible_keys:
                try:
                    key_bytes = hashlib.sha256(master_key.encode()).digest()
                    decrypted_info = self._xor_encrypt(encrypted_info, key_bytes)
                    info_json = decrypted_info.decode()

                    # 验证哈希
                    if hashlib.sha256(info_json.encode()).hexdigest() == stored_hash:
                        key_info = json.loads(info_json)
                        self._salt = base64.b64decode(key_info['salt'])
                        self._key_version = key_info.get('key_version', 1)
                        return

                except Exception:
                    continue

            raise ValueError("无法解密密钥信息")

        except Exception as e:
            logger.error(f"加载密钥信息失败: {e}")
            raise

    def _xor_encrypt(self, data: bytes, key: bytes) -> bytes:
        """简单的XOR加密"""
        result = bytearray()
        key_len = len(key)
        for i, byte in enumerate(data):
            result.append(byte ^ key[i % key_len])
        return bytes(result)

    def _use_emergency_keys(self):
        """使用应急密钥系统"""
        logger.warning("使用应急密钥系统")
        self._salt = b'emergency_salt_2024_secure_license'
        self._key_version = 0


    def _derive_key_with_argon2(self, master_key: str) -> bytes:
        """使用Argon2派生密钥"""
        if not ARGON2_AVAILABLE:
            return self._derive_key_with_pbkdf2(master_key)

        try:
            # Argon2参数
            time_cost = 3      # 时间成本
            memory_cost = 65536  # 内存成本 (64MB)
            parallelism = 1    # 并行度

            derived_key = hash_secret(
                secret=master_key.encode(),
                salt=self._salt,
                time_cost=time_cost,
                memory_cost=memory_cost,
                parallelism=parallelism,
                hash_len=32,
                type=Type.ID
            )
            return derived_key

        except Exception as e:
            logger.warning(f"Argon2密钥派生失败，回退到PBKDF2: {e}")
            return self._derive_key_with_pbkdf2(master_key)

    def _derive_key_with_pbkdf2(self, master_key: str) -> bytes:
        """使用PBKDF2派生密钥"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=self._salt,
            iterations=200000,  # 增加迭代次数
        )
        return kdf.derive(master_key.encode())

    def _get_fernet(self) -> Fernet:
        """获取Fernet加密实例"""
        if self._fernet is None:
            # 重新生成主密钥
            hardware_fp = self._generate_hardware_fingerprint()

            if self._master_key:
                combined_key = f"{self._master_key}-{hardware_fp}"
            else:
                combined_key = f"SecureLicense-{hardware_fp}-2024"

            # 使用Argon2或PBKDF2派生密钥
            derived_key = self._derive_key_with_argon2(combined_key)

            # 创建Fernet实例
            fernet_key = base64.urlsafe_b64encode(derived_key)
            self._fernet = Fernet(fernet_key)

        return self._fernet

    def encrypt_data(self, data: Dict[str, Any]) -> str:
        """加密数据"""
        try:
            json_data = json.dumps(data, ensure_ascii=False, separators=(',', ':'))
            encrypted = self._get_fernet().encrypt(json_data.encode('utf-8'))
            return base64.urlsafe_b64encode(encrypted).decode('utf-8')
        except Exception as e:
            logger.error(f"数据加密失败: {e}")
            raise ValueError(f"数据加密失败: {e}")

    def decrypt_data(self, encrypted_data: str) -> Dict[str, Any]:
        """解密数据"""
        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode('utf-8'))
            decrypted = self._get_fernet().decrypt(encrypted_bytes)
            return json.loads(decrypted.decode('utf-8'))
        except Exception as e:
            logger.error(f"数据解密失败: {e}")
            raise ValueError(f"许可证解密失败: {e}")

    def generate_signature(self, data: Dict[str, Any]) -> str:
        """生成数据签名"""
        try:
            json_data = json.dumps(data, sort_keys=True, ensure_ascii=False, separators=(',', ':'))

            # 使用派生的签名密钥
            signing_key = self._get_signing_key()
            signature = hmac.new(
                signing_key,
                json_data.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()

            return signature
        except Exception as e:
            logger.error(f"签名生成失败: {e}")
            raise ValueError(f"签名生成失败: {e}")

    def verify_signature(self, data: Dict[str, Any], signature: str) -> bool:
        """验证数据签名"""
        try:
            expected_signature = self.generate_signature(data)
            return hmac.compare_digest(expected_signature, signature)
        except Exception as e:
            logger.error(f"签名验证失败: {e}")
            return False

    def _get_signing_key(self) -> bytes:
        """获取签名密钥"""
        # 基于主密钥和硬件指纹生成签名密钥
        hardware_fp = self._generate_hardware_fingerprint()

        if self._master_key:
            combined_key = f"SIGN-{self._master_key}-{hardware_fp}"
        else:
            combined_key = f"SIGN-SecureLicense-{hardware_fp}-2024"

        # 使用不同的盐值派生签名密钥
        sign_salt = hashlib.sha256(self._salt + b'signature').digest()

        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=sign_salt,
            iterations=150000,
        )
        return kdf.derive(combined_key.encode())

    def rotate_keys(self) -> bool:
        """密钥轮换"""
        try:
            logger.info("开始密钥轮换")

            # 备份当前密钥信息
            backup_file = self.key_info_file.with_suffix('.backup')
            if self.key_info_file.exists():
                import shutil
                shutil.copy2(self.key_info_file, backup_file)

            # 增加密钥版本
            self._key_version += 1

            # 重新生成密钥信息
            self._fernet = None  # 重置Fernet实例
            self._generate_new_key_info()

            logger.info(f"密钥轮换成功，新版本: {self._key_version}")
            return True

        except Exception as e:
            logger.error(f"密钥轮换失败: {e}")
            return False

# 保持向后兼容的旧版加密管理器
class CryptoManager(SecureCryptoManager):
    """向后兼容的加密管理器"""

    def __init__(self, master_key: str = None):
        # 使用旧的固定密钥作为主密钥（如果没有提供）
        if master_key is None:
            master_key = "OfflineCLI_MasterKey_2024"
        super().__init__(master_key=master_key)

# 全局加密管理器实例（使用新的安全版本）
crypto_manager = SecureCryptoManager()

# 向后兼容的实例
legacy_crypto_manager = CryptoManager()